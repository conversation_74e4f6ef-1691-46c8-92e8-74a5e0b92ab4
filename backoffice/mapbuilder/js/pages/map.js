import BaseLayerControl from '../components/baselayercontrol.js';
import MapManager from '../components/mapmanager.js';
import MapViewControl from '../components/mapviewcontrol.js';
import LegendControl from '../components/legendcontrol.js';
import LasooControl from '../components/lassocontrol.js';
import ContentEditable from '../components/utility/contenteditable.js';
import UtilityLib from "../components/utility/utilitylib.js";
import PrintControl from "../components/printcontrol.js";
import DropDownMultipleDatatableHeader from "../components/utility/dropdownmultipledatatableheader.js"
import LayerManager from "../components/layermanager.js"
import { CustomDivIcon } from '../components/custom-div-icon.js';
import * as polyclip from "../imports/polyclip/polyclip-ts.esm.min.js";
import TabulatorDatatable from '../../../core/js/components/tabulator/tabulator-datatable.js';

export default {
    template: /*html*/`<div id="map-page">
    <v-app-bar v-resize="onResize" v-if="$route.name=='map'" app fixed elevate-on-scroll clipped-left class="mainback"
               style="border-bottom:1px solid #e5e5e5;">
        <v-app-bar-nav-icon @click.stop="mini = !mini"></v-app-bar-nav-icon>
        <div class="">
            <img src="/images/sitefotos_logo_icon.svg" style="width:44px; height:44px;padding-right:10px;">
        </div>
        <span class="page-titles">{{$route.meta.ptitle}}</span>
     
        <v-spacer></v-spacer>
        <v-btn  text v-if="takeoffUser==false && (pagePermissionEdit || pagePermissionAdd)" @click="openOldMapbuilder" class="normal-spacing stop-caps hidden-sm-and-down "> Old Mapbuilder </v-btn>
        <v-btn  text v-if="takeoffUser==false && pagePermissionAdd" @click="newMap" class="normal-spacing stop-caps hidden-sm-and-down "> New </v-btn>
        <v-btn :loading="btnSaveLoader" v-if="mapState=='editing'  && (pagePermissionEdit || pagePermissionAdd)" text @click="trySave" class="normal-spacing stop-caps hidden-sm-and-down "> {{saveText}} </v-btn>

        <v-btn v-if="mapState=='editing' && takeoffUser==false  && (pagePermissionEdit || pagePermissionAdd)" text @click="trySaveAs" class="normal-spacing stop-caps hidden-sm-and-down "> Save As </v-btn>
        <v-btn v-if="takeoffUser==false" text class="normal-spacing stop-caps hidden-sm-and-down "
               @click="internalFileManager=true">
            Open
        </v-btn>
        <v-btn v-if="takeoffUser==false && (pagePermissionEdit || pagePermissionAdd)" text class="normal-spacing stop-caps hidden-sm-and-down "
        @click="assignSite">
     Assign Site
 </v-btn>


 <print-control></print-control>
 <layer-manager></layer-manager>
        

        <v-btn v-if="takeoffUser==false && (pagePermissionEdit || pagePermissionAdd)" text class="normal-spacing stop-caps hidden-sm-and-down "
               @click="mergemapDialog=true">
            Merge
        </v-btn>
        <v-btn v-if="fileID!=0 && takeoffUser==false" text class="normal-spacing stop-caps hidden-sm-and-down "
               @click="shareMapDialog=true">
            Share
        </v-btn>
        <div class="nav-block" v-if="takeoffUser==false">
            <v-img :src="cLogo" max-height="36" max-width="72" contain :alt="cName" style="display: inline-block"></v-img>

            <v-menu offset-y bottom style="max-width: 200px">
                <template v-slot:activator="{ on, attrs }">
                    <v-avatar color="purple" size="36" class="ml-2" v-bind="attrs" v-on="on">
                        <span class="white--text headline" style="font-size:1.2rem!important;">{{initials}}</span>
                    </v-avatar>
                </template>
                <v-list>
                    <v-list-item @click="openSettings">
                        <v-list-item-title>Settings</v-list-item-title>
                    </v-list-item>
                    <v-list-item @click="logout()">
                        <v-list-item-title>Log Off</v-list-item-title>
                    </v-list-item>
                </v-list>
            </v-menu>
        </div>

        <v-menu offset-y :close-on-content-click='false'>
            <template v-slot:activator="{ on }">
                <v-app-bar-nav-icon v-on="on" class="hidden-md-and-up">
                    <v-icon>more_vert</v-icon>
                </v-app-bar-nav-icon>
            </template>
            <v-card>
                <v-list dense>

                </v-list>
            </v-card>
        </v-menu>

    </v-app-bar>

    <div id="mapcontainer" v-show="$route.name=='map'"
         style="min-width:100%; width: 100%;height:100%">

        <div id="map" v-bind:class="{'disabled-map': mapState!='editing', 'sidebar-map':true}"
             style="position:absolute;width:inherit;height:100%;z-index:0">

            <legend-control v-show="$route.name=='map'" style="overflow-y:none; overflow-x:none;"></legend-control>

        </div>
       
        <base-layer-control v-show="$route.name=='map'" :estimator="estimator"
                            :marginTop="marginTop"></base-layer-control>
        <map-view-control v-show="$route.name=='map'" :estimator="estimator"
                          :marginTop="marginTop"></map-view-control>
        <map-manager :fit-bounds="fitBounds" :on-poly-click='onPolyClick'
                     :unset-highlight-layer="unsetHighlightLayer" :set-highlight-layer="setHighlightLayer"
                     v-show="$route.name=='map'"></map-manager>
        <lasoo-control v-show="$route.name=='map'"></lasoo-control>


    </div>
    <v-card v-show="$route.name=='map'" v-if="itemPopup" v-bind:class="itemPropClass" ref="itemPopup"
            id="item-prop-popup" :style="itemPropStyle">
        <v-toolbar dense height="36px" class="elevation-0 white">
            <v-toolbar-title>
                <h3 class="subtitle-1 font-weight-black mb-0">
                    <content-editable @change="forceUpdateMapManager"
                                      v-model="selectedItem.feature.properties.name"></content-editable>
                </h3>
            </v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn small icon @click="itemPopup = false">
                <v-icon>close</v-icon>
            </v-btn>
        </v-toolbar>
        <div class="pl-3 pr-3">
            <v-row no-gutters
                   v-if="selectedItem.feature.properties.type=='Circle' || selectedItem.feature.properties.type=='Rectangle' || selectedItem.feature.properties.type=='Polygon'">
                <v-col cols="12">
                    <span class="caption font-italic font-weight-bold">{{selectedItem.feature.properties.area}}</span>


                    <span class="ml-4 caption font-italic font-weight-bold">{{selectedItem.feature.properties.perimeter}}</span>
                </v-col>
            </v-row>
            <v-row no-gutters v-if="selectedItem.feature.properties.type=='Line'">
                <v-col cols="12" sm="12" md="12" lg="12">
                    <span class="caption font-italic font-weight-bold"> {{selectedItem.feature.properties.perimeter}}</span>
                </v-col>
            </v-row>
            <v-row no-gutters v-if="selectedItem.feature.properties.type=='Marker'">
                <v-col cols="6" sm="6" md="6" lg="6">
                    <v-menu eager v-model="itemPopupMarkerMenu" offset-y :close-on-content-click='false'>
                        <template v-slot:activator="{ on }">
                            <v-btn v-on="on" color="primary" class="stop-caps">Change Icon</v-btn>
                        </template>
                        <v-card>

                            <v-tabs>
                                <v-tab v-for="markertype in markers.type" :key="markertype.name">
                                    {{markertype.name}}
                                </v-tab>


                                <v-tab-item style="max-width:250px;max-height:200px" v-for="markertype in markers.type"
                                            :key="markertype.name">
                                    <v-card flat>
                                        <v-card-text>
                                            <v-row>

                                                <template v-for="marker in markertype.icons">
                                                    <v-col cols="3">
                                                        <v-btn text @click="changeMarker(marker)"><img
                                                                :src="marker.iconurl"
                                                                style="width: 26px !important;height: 40px !important;">
                                                        </v-btn>
                                                    </v-col>
                                                </template>

                                            </v-row>
                                        </v-card-text>
                                    </v-card>
                                </v-tab-item>
                            </v-tabs>
                        </v-card>
                    </v-menu>
                </v-col>
                <v-col cols="6" sm="6" md="6" lg="6" style="text-align:center;">
                    <img :src="selectedItem.feature.properties.markerURL" class="cell-icon"></img>
                </v-col>
            </v-row>
            <v-row no-gutters v-if="selectedItem.feature.properties.type=='Photo'" class="mb-2">
                <v-col cols="12">
                    <v-img class="mx-auto" max-width="190px" :src="selectedItem.feature.properties.imageURL"></v-img>
                </v-col>
            </v-row>
            <v-row no-gutters v-if="selectedItem.feature.properties.type=='Photo'">
                <v-col cols="6" sm="6" md="6" lg="6">
                    <v-menu eager v-model="itemPopupPhotoMenu" offset-y :close-on-content-click='false'>
                        <template v-slot:activator="{ on }">
                            <v-btn v-on="on" color="primary" class="stop-caps">Change Icon</v-btn>
                        </template>
                        <v-card style="max-width:250px;max-height:200px">
                            <v-card-text>

                                <v-row>

                                    <template v-for="marker in photoMarkers">
                                        <v-col cols="3">
                                            <v-btn text @click="changePhotoMarker(marker)"><img :src="marker.iconurl"
                                                                                                style="width: 26px !important;height: 40px !important;">
                                            </v-btn>
                                        </v-col>
                                    </template>

                                </v-row>
                            </v-card-text>

                        </v-card>
                    </v-menu>
                </v-col>
                <v-col cols="6" sm="6" md="6" lg="6" style="text-align:center;">
                    <img :src="selectedItem.feature.properties.markerURL" class="cell-icon" />
                </v-col>
            </v-row>
            <v-row no-gutters
                   v-if="selectedItem.feature.properties.type!='Marker' && selectedItem.feature.properties.type!='Text' && selectedItem.feature.properties.type!='Photo'">
                <v-col cols="5" class="font-weight-regular caption" style="align-self: center;">Change Layer</v-col>
                <v-col cols="7">

                    <v-autocomplete height="12" outlined hide-details :value="selectedItem.feature.properties.preset"
                                    :items="groupItems"
                                    dense
                                    item-value="value"
                                    item-text="text" @change="changeItemPreset"></v-autocomplete>

                </v-col>

            </v-row>
            <v-row no-gutters v-if="selectedItem.feature.properties.type=='Line'">
                <v-col cols="5">
                    <v-switch label="Animation" dense hide-details v-model="animStatus"></v-switch>
                </v-col>
                <v-col cols="4" style="text-align: center;transform: rotate(90deg);"> <img :src="selectedItem.feature.properties.AnimIcon" class="cell-icon" /></v-col>
                <v-col cols="3">  <v-menu eager v-model="itemPopupMarkerMenu" offset-y :close-on-content-click='false'>
                        <template v-slot:activator="{ on }">
                            <v-btn v-on="on" color="primary" icon class="stop-caps"><v-icon>edit</v-icon></v-btn>
                        </template>
                       <v-card style="max-width:250px;max-height:200px">
                            <v-card-text>

                                <v-row>

                                    <template v-for="marker in animMarkers">
                                        <v-col cols="3">
                                            <v-btn text @click="changeAnimIcon(marker)"><img :src="marker.iconurl"
                                                                                                style="width: 26px !important;height: 40px !important;transform: rotate(90deg)">
                                            </v-btn>
                                        </v-col>
                                    </template>

                                </v-row>
                            </v-card-text>

                        </v-card>


                    </v-menu></v-col>
            </v-row>
            <v-row no-gutters>
            <v-textarea hide-details dense outlined @input="descriptionChange"
                        :value="fixDescription(selectedItem.feature.properties.description)"
                        :rows="selectedItem.feature.properties.type=='Text' ? 2 : 1"
                        placeholder="Description" style="margin-top:5px;"
            ></v-textarea>

            </v-row>
        </div>
        <v-card-actions class="pt-1">
            <v-row no-gutters>
                <template v-if="selectedItem.feature.properties.type=='Polygon' || selectedItem.feature.properties.type=='Rectangle'">
                    <v-col cols="3">
                      

                                <v-btn title="Rotate" icon @click="rotate">
                                    <v-icon color="grey darken-3" size="1rem">fa-rotate-right</v-icon>
                                </v-btn>

                         
                   
                    </v-col>

                </template>
                <template v-if="selectedItem.feature.properties.type=='Text'">
                    <v-col cols="3">
                        <v-text-field outlined dense class="mt-0" type="number" dense @input="fontSizeChange" hide-details
                                      :value="selectedItem.feature.properties.fontSize">

                        </v-text-field>
                    </v-col>

                </template>
                <v-spacer></v-spacer>
                <v-dialog overlay-opacity="0" v-model="cutHoleDialog" width="500"
                          v-if="selectedItem.feature.properties.type=='Polygon' || selectedItem.feature.properties.type=='Rectangle'">
                    <template v-slot:activator="{ on }">
                        <v-col cols="auto">
                            <v-btn title="Cut Hole in shape Below" v-on="on" icon>
                                <v-icon color="grey darken-3" size="1.25rem">fa-scissors</v-icon>
                            </v-btn>
                        </v-col>
                    </template>
                    <v-card>
                        <v-toolbar class="elevation-0 white">
                            <v-toolbar-title>
                                <h3 class="headline mb-0">Cut Hole</h3>
                            </v-toolbar-title>
                            <v-spacer></v-spacer>
                            <v-btn icon @click="cutHoleDialog=false">
                                <v-icon>close</v-icon>
                            </v-btn>
                        </v-toolbar>
                        <v-card-text>Please select the type of hole that you want to cut.
                            <v-radio-group v-model="cutHoleType" row>
                                <v-radio label="Cut and Remove" value="cutremove"></v-radio>
                                <v-radio label="Cut and Replace" value="cutreplace"></v-radio>
                            </v-radio-group>
                        </v-card-text>
                        <v-card-actions>
                            <v-spacer></v-spacer>
                            <v-btn color="primary" :loading="cutHoleLoading" @click="cutHole()">Submit</v-btn>
                        </v-card-actions>
                    </v-card>
                </v-dialog>
                <v-col cols="auto">
                    <v-btn title="Add/Remove Arrow" @click="addArrow" icon
                           v-if="selectedItem.feature.properties.type=='Line'">
                        <template v-if="typeof selectedItem.feature.properties.arrow != 'undefined'">
                            <v-icon color="grey darken-3" size="1.25rem"
                                    v-if="selectedItem.feature.properties.arrow == 'none'">
                                fa-arrow-right
                            </v-icon>
                            <v-icon v-else color="grey darken-3" size="1.25rem">fa-arrow-right</v-icon>


                        </template>
                        <template v-else>
                            <v-icon color="grey darken-3" size="1.25rem">fa-arrow-right</v-icon>

                        </template>
                    </v-btn>
                </v-col>
                <v-col cols="auto">
                    <v-btn title="Move Item" @click="toggleEditingMode()" icon
                           :outlined="selectedItem.pm.options.mydragging"
                           v-if="selectedItem.feature.properties.type!='Photo' && selectedItem.feature.properties.type!='Text' && selectedItem.feature.properties.type!='Marker'">
                        <v-icon color="grey darken-3" size="1.25rem">fa-arrows</v-icon>
                    </v-btn>
                </v-col>
                <v-col cols="auto">
                <v-btn title="Background" @click="textBackgroundToggle" icon
                           v-if="selectedItem.feature.properties.type=='Text'">
                        <template v-if="selectedItem.feature.properties?.background ?? false == true">
                            <v-icon color="grey darken-3" size="1.25rem">
                            mdi-invert-colors
                            </v-icon>

                        </template>
                        <template v-else>
                            <v-icon color="grey darken-3" size="1.25rem">mdi-invert-colors-off</v-icon>

                        </template>
                    </v-btn>
                </v-col>
                <v-col cols="auto">
                <v-menu v-model="textColorMenu" offset-x :close-on-content-click="true" v-if="selectedItem.feature.properties.type=='Text'">
                <template v-slot:activator="{ on }">
                    <div class="text-swatch-button" title="Text Color" :style="{backgroundColor: selectedItem.feature.properties?.textColor || '#FFFFFF'}"
                         v-on="on"/>
                </template>
                <v-color-picker v-model="selectedItem.feature.properties.textColor" :swatches="swatches"
                                class="layer-manager-color-picker" hide-inputs hide-mode-switch hide-canvas
                                show-swatches
                                @input="textColorChange" 
                                swatches-max-height="300px"></v-color-picker>
            </v-menu>
                </v-col>
                <v-col cols="auto">
                    <v-btn v-if="selectedItem.feature.properties.type!='Photo' && selectedItem.feature.properties.type!='Text'"
                           title="Draw Another" @click="drawAnother()" icon
                    >
                        <v-icon color="grey darken-3" size="1.25rem">fa-plus</v-icon>
                    </v-btn>
                </v-col>
                <v-col cols="auto">
                    <v-btn v-if="selectedItem.feature.properties.type!='Photo'" title="Copy Item"
                           @click="copyLayer(selectedItem._leaflet_id)" icon>
                        <v-icon color="grey darken-3" size="1.25rem">fa-copy</v-icon>
                    </v-btn>
                </v-col>
                <v-col cols="auto">
                    <v-btn title="Delete Item"
                           @click="deleteLayer(selectedItem._leaflet_id)" icon>
                        <v-icon color="grey darken-3" size="1.25rem">fa-trash</v-icon>
                    </v-btn>
                </v-col>
            </v-row>
        </v-card-actions>
    </v-card>


   <v-dialog absolute v-model="internalFileManager" 
    :fullscreen="$vuetify.breakpoint.smAndDown"
    :hide-overlay="$vuetify.breakpoint.smAndDown" 
    width="85vw" 
    transition="dialog-bottom-transition">
    <v-card flat height="62vh" style="display: flex; flex-direction: column;">
        <!-- Progress Bar for Loading -->
        <v-progress-linear
            v-if="fileManagerloading"
            indeterminate
            color="blue"
            style="margin: 0;"
        ></v-progress-linear>

        <!-- Tabulator Table -->
        <tabulator-datatable
        title="Open Map"
            ref="mapTabulatorTable"
            :tabulator-configuration="tabulatorConfig"
            refresh-type="local"
            in-dialog
            :show-clear-all-filters-button="true"
            style="flex: 1 1 auto; overflow: hidden;"
        ></tabulator-datatable>
    </v-card>
</v-dialog>

    <router-view></router-view>
    <v-snackbar v-model="snackbar.snackbar" :bottom="snackbar.y === 'bottom'" :left="snackbar.x === 'left'"
                :right="snackbar.x === 'right'" :timeout="snackbar.timeout" :top="snackbar.y === 'top'">{{
        snackbar.text }}
        <v-btn color="pink" text @click="snackbar.snackbar = false">Close</v-btn>
    </v-snackbar>
    <UtilityLib ref="utilityLib"></UtilityLib>
</div>`,
    components: {
        BaseLayerControl,
        MapManager,
        ContentEditable,
        MapViewControl,
        LegendControl,
        LasooControl,
        PrintControl,
        DropDownMultipleDatatableHeader,
        LayerManager,
        TabulatorDatatable,
        UtilityLib
    },

    watch: {
        files: {
            handler(newVal) {
                if (this.$refs.mapTabulatorTable?.tabulator) {
                    this.$refs.mapTabulatorTable.tabulator.setData(newVal);
                }
            },
            deep: true
        },
        internalFileManager(newVal) {
            if (newVal) {
                // Force data refresh when dialog opens
                this.updateData().then(() => {
                    this.fileManagerloading = true;
                    this.$nextTick(() => {
                        if (this.$refs.mapTabulatorTable?.tabulator) {
                            this.$refs.mapTabulatorTable.tabulator.setData(this.files);
                            this.$refs.mapTabulatorTable.tabulator.redraw(true);
                            this.fileManagerloading = false;
                        }
                    });
                });
            }
        },


        itemPopup: function (val) {


            this.itemPopupMarkerMenu = false;
            this.cutHoleDialog = false;
            this.itemPopupPhotoMenu = false;
            this.textColorMenu = false;
        },
        selectedItem: function (newVal, oldVal) {
            if (newVal != oldVal) {
                if (oldVal) {
                    oldVal.pm.options.mydragging = false;
                    this.$forceUpdate();
                    oldVal.pm.disableLayerDrag()
                }
            }
        },
        '$route.name': function (newval) {
            if (newval == 'map') {
                Vue.nextTick(function () {
                    map.invalidateSize(false)
                });
            }
            if (newval == 'fileman') {
                this.internalFileManager = false;
                this.cutHoleDialog = false;
                globalMapManager.hideAssignSiteDialog();
                globalMapManager.hidenewMapModel();
            }
        }

    },

    name: 'map-page',
    created() {
        this.updateData();
        window.globalVueMapInstance = this;
        //Static Non reactive data
        this.highlightedLayer = null;
        this.disableEditing = false;
        this.currentlyEditing = null;
        this.continuousmodeFlag = false;
        this.lastdragevent = Date.now();
    },

    activated() {

        if (store.get('map/fitBounds')) {
            Vue.nextTick(function () {
                if (drawnGroups.getBounds().isValid())
                    map.fitBounds(drawnGroups.getBounds());
                else {
                    let myzoomBounds = drawnItems.getBounds();

                    if (myzoomBounds.isValid()) {
                        map.fitBounds(myzoomBounds);
                        if (map.getBoundsZoom(myzoomBounds) > 18)
                            map.setZoom(18);


                    } else {
                        let maxBounds = L.latLngBounds(
                            L.latLng(24.0, -128.23),
                            L.latLng(50.09, -59.14)
                        );
                        map.fitBounds(maxBounds);
                        map.setZoom(4);
                    }
                }
                map.invalidateSize(false);
                store.set('map/fitBounds', false);
            }.bind(this))
        } else {
            if (map != null)
                map.invalidateSize(false);
        }
    },

    mounted() {
        console.info('Map Page Mounted');
        this.extendLeaflet()
        this.initializeMap()
    },
    unmounted() {
        console.info('Map Page Unmounted');
    },
    beforeDestroy() {
        // Clean up map event listeners
        if (map) {
            map.off('click');
            map.off('movestart');
            map.off('moveend');
            map.off('load');
            map.off('zoomend');
            map.off('pm:create');
            map.off('pm:drawstart');
            map.off('pm:drawend');
            map.off('pm:markerdragend');
            map.off('updatelegend');
            map.off('autosave');
            map.off('addtextarea');
            map.off('continuousmode');
            map.off('addedges');

            // Remove the map instance
            map.remove();
        }

        // Clean up layer event listeners
        if (drawnGroups) {
            drawnGroups.eachLayer(layer => {
                layer.off('click', this.onPolyClick);
                layer.off('mouseover');
                layer.off('mouseout');
                layer.off('layeradd', this.updateGroups);
                layer.off('layerremove', this.updateGroups);
                if (layer.pm) {
                    layer.pm.disable();
                }
            });
        }

        // Clean up animations and arrowheads
        if (this.animations) {
            this.animations.forEach(([_, __, animLayer]) => {
                if (map && animLayer) {
                    map.removeLayer(animLayer);
                }
            });
            this.animations = [];
        }

        if (this.arrowHeads) {
            this.arrowHeads.forEach(([_, __, arrowLayer]) => {
                if (map && arrowLayer) {
                    map.removeLayer(arrowLayer);
                }
            });
            this.arrowHeads = [];
        }

        // Clean up presets
        if (this.presets) {
            Object.values(this.presets).forEach(preset => {
                if (preset.leafletlayer) {
                    preset.leafletlayer.clearLayers();
                    preset.leafletlayer.off('layeradd', this.updateGroups);
                    preset.leafletlayer.off('layerremove', this.updateGroups);
                }
            });
        }

        // Restore original Leaflet prototypes
        if (this.originalMarkerPrototype) {
            Object.assign(L.Marker.prototype, this.originalMarkerPrototype);
        }
        if (this.originalIconPrototype) {
            Object.assign(L.Icon.prototype, this.originalIconPrototype);
        }
        if (this.originalLayerGroupPrototype) {
            Object.assign(L.LayerGroup.prototype, this.originalLayerGroupPrototype);
        }
        if (this.originalPolygonPrototype) {
            Object.assign(L.Polygon.prototype, this.originalPolygonPrototype);
        }

        // Clear prototype references
        this.originalMarkerPrototype = null;
        this.originalIconPrototype = null;
        this.originalLayerGroupPrototype = null;
        this.originalPolygonPrototype = null;

        // Clean up global variables
        window.globalVueMapInstance = null;
        window.map = null;
        delete window.globalVueMapInstance;

        // Clear any remaining references
        this.selectedItem = null;
        this.currentlyEditing = null;
        this.highlightedLayer = null;
        this.presets = null;
        //this.groups = null;
    },
    data: function () {
        return {
            btnSaveLoader: false,
            tabulatorConfig: {
                    persistence: {
                    sort: false,
                    filter: false,
                    columns: false
                },
                printStyled: true,
                movableColumns: true,
                movableRows: false,
                resizableRows: false,
                height: "100%",
                layout: "fitColumns",
                pagination: true,
                paginationMode: "local",
                paginationSize: 100,
                paginationSizeSelector: [100, 250, 500, 1000],
                paginationCounter: "rows",
                selectableRows: false,
                columns: [
                    {
                        title: "Map Name",
                        field: "LayerName",
                        width: 270,
                        hozAlign: "left",
                        headerHozAlign: "center",
                        frozen: false,
                        headerFilter: "input",
                        headerFilterLiveFilter: false,
                        formatter: function(cell) {
                            return '<span style="color: #1867c0;font-weight:bold; cursor: pointer; text-decoration: none;">' + cell.getValue() + '</span>';
                        },
                        cellClick: function(e, cell) {
                           this.editMap(cell.getRow().getData())
                           this.internalFileManager = false;

                        }.bind(this)
                    },
                    {
                        title: "Site",
                        field: "sitename",
                        width: 270,
                        hozAlign: "left",
                        headerHozAlign: "center",
                        headerFilter: "input",
                        headerFilterLiveFilter: false,

                    },
                    {
                        title: "Address",
                        field: "mb_address1",
                        hozAlign: "left",
                        headerHozAlign: "center",
                        headerFilter: "input",
                        headerFilterLiveFilter: false
                    },
                    {
                        title: "City",
                        field: "cityname",
                        hozAlign: "center",
                        headerHozAlign: "center",
                        headerFilter: "input",
                        headerFilterLiveFilter: false
                    },
                    {
                        title: "State",
                        width: 100,
                        field: "statename",
                        hozAlign: "center",
                        headerHozAlign: "center",
                        headerFilter: "list",
                        headerFilterParams: {
                            multiselect: true,
                            valuesLookup: true,
                            clearable: true
                        }
                    },
                    {
                        title: "Creator",
                        field: "CreatorName",
                        hozAlign: "center",
                        headerHozAlign: "center",
                        headerFilter: "list",
                        headerFilterParams: {
                            multiselect: true,
                            valuesLookup: true,
                            clearable: true
                        }
                    },
                    {
                        title: "Last Editor",
                        field: "UpdaterName",
                        hozAlign: "center",
                        headerHozAlign: "center",
                        headerFilter: "list",
                        headerFilterParams: {
                            multiselect: true,
                            valuesLookup: true,
                            clearable: true
                        }
                    },
                    {
                        title: "Date Modified",
                        field: "datetime",
                        hozAlign: "center",
                        headerHozAlign: "center"
                    },
                    {
                        title: "Actions",
                        field: "actions",
                        width: 90,
                        hozAlign: "center",
                        headerHozAlign: "center",
                        headerSort: false,
                        formatter: function(cell) {
                            return '<i class="v-icon notranslate mdi mdi-arrow-down-circle-outline theme--light" style="cursor: pointer;" title="Download Measurements"></i>';
                        },
                        cellClick: function(e, cell) {
                            e.stopPropagation(); // Prevent row click
                            this.downloadMeasurements(cell.getRow().getData());
                        }.bind(this)
                    }
                ]
            },
            textColorMenu: false,
            swatches: [
                ["#000000", "#434343", "#666666"],
                ["#cccccc", "#d9d9d9", "#ffffff"],
                ["#980000", "#ff0000", "#ff9900"],
                ["#C3FDB8", "#ffff00", "#00ff00"],
                ["#00ffff", "#4a86e8", "#0000ff"],
                ["#9900ff", "#ff00ff", "#e6b8af"],
                ["#f4cccc", "#fce5cd", "#fff2cc"],
                ["#d9ead3", "#d0e0e3", "#c9daf8"],
                ["#cfe2f3", "#d9d2e9", "#ead1dc"],
                ["#dd7e6b", "#ea9999", "#f9cb9c"],
                ["#ffe599", "#b6d7a8", "#a2c4c9"],
                ["#a4c2f4", "#9fc5e8", "#b4a7d6"],
                ["#d5a6bd", "#cc4125", "#e06666"],
                ["#f6b26b", "#ffd966", "#93c47d"],
                ["#76a5af", "#6d9eeb", "#6fa8dc"],
                ["#8e7cc3", "#c27ba0", "#a61c00"],
                ["#cc0000", "#e69138", "#f1c232"],
                ["#6aa84f", "#45818e", "#3c78d8"],
                ["#3d85c6", "#674ea7", "#a64d79"],
                ["#5b0f00", "#660000", "#783f04"],
                ["#7f6000", "#274e13", "#0c343d"],
                ["#1c4587", "#073763", "#20124d"],
                ["#4c1130", "#9e8000", "#9900b7"],
                ["#9e2300", "#b223b7", "#556666"],
                ["#69aeb7", "#b2aeb7", "#9e80b7"],
                ["#008000", "#66FF00", "#9933CC"],
                ["#389AFF", "#66FF99", "#228822"],
                ["#99CC00", "#888888", "#3366FF"],
                ["#3399FF", "#FF00FF", "#ED3B4A"],
                ["#F58EA7", "#C7A317", "#AF7817"]
            ],
            filter: {
                sites: [],
                creator: [],
                updater: []
            },

            estimator: false,
            expanded: [],
            expandedAuto: [],
            marginTop: '64px',
            searchFileManager: '',
            internalFileManager: false,
            fileManagerloading: false,
            itemPopup: false,
            files: [],
            auto: [],
            drawing: false,
            itemPropStyle: {
                top: null,
                left: null,
            },
            pagination: {
                rowsPerPage: 10,
                sortBy: ["modified"],
                sortDesc: [true]
            },
            openmapdata: {
                mapid: 0,
                mapname: '',
                auto: false,
                autoid: '',
                build: 0
            },
            forceRecompute: 0,
            rotateValue: 90,
            cutHoleDialog: false,
            cutHoleType: null,
            cutHoleLoading: false,
            itemPropClass: [],
            selectedItem: null,
            itemPopupMarkerMenu: false,
            itemPopupPhotoMenu: false,
            snackbar: {
                snackbar: false,
                y: 'bottom',
                x: 'left',
                mode: '',
                timeout: 2000,
                text: ''
            },
        };
    },

    methods: {

        closeDialog() {
            this.internalFileManager = false;
        },
       shouldShowDraw() {
            if (mytem == 178 || myvtem == 10738)
                return true;
            else
                return false;
        },
        drawEdges() {
            console.log("got here")
            globalMapManager.drawEdges();
        },
        openOldMapbuilder() {
            window.open('/vpics/mapbuilderdev', '_blank');

        },
        groupBy(array, f) {
            var groups = {};
            array.forEach(function (o) {
                var group = JSON.stringify(f(o));
                groups[group] = groups[group] || [];
                groups[group].push(o);
            });
            return Object.keys(groups).map(function (group) {
                return groups[group];
            })
        },
        async downloadMeasurements(item) {

            const formBody = new URLSearchParams();
            formBody.append("accessCode", store.get('map/accessCode'));
            formBody.append("LayerID", item.LayerID);
            formBody.append("debugData", "map.js => downloadMeasurements");
            let response = await fetch(myBaseURL + '/node/maps/open-map-layer', {
                method: 'POST',
                body: formBody,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            });

            if (response.ok) {
                let data = await response.text()
                let savedData = null;
                if (data.slice(-6).indexOf('}') == -1) {
                    savedData = JSON.parse(data.substring(0, data.length -
                        6));
                } else {
                    savedData = JSON.parse(data);
                }
                savedData = JSON.parse(savedData)
                savedData = await globalMapManager.cleanSavedData(savedData);
                console.log(savedData)
                for (let t = 0; t < savedData.length; t++) {
                    let boo = L.geoJSON(savedData[t].features);
                    let b = boo.getBounds();
                    if (b.isValid() && savedData[t].metadata.vectorgroup) {
                        let area = turf.area(L.rectangle(b).toGeoJSON(15))
                        savedData[t].area = area
                    } else {
                        savedData[t].area = 0;
                    }
                }

                savedData.sort(function (a, b) {
                    return parseFloat(b.area) - parseFloat(a.area);
                });
                console.log(savedData)
                var k = [];
                var x = 0;
                for (let featureGroup of savedData) {
                    if (featureGroup.metadata.vectorgroup) {
                        for (let glayer of featureGroup.features) {

                            k[x] = {};
                            k[x].colName = glayer.properties.name;
                            if (featureGroup.metadata.name == 'none') {
                                k[x].colGroup = "Ungrouped";
                            } else {
                                k[x].colGroup = featureGroup.metadata.name;
                            }
                            if (typeof glayer.properties.area !== 'undefined')
                                k[x].colArea = parseInt(glayer.properties.area
                                    .replace(
                                        /\D/g, ''));
                            else
                                k[x].colArea = 0;
                            k[x].colPerim = parseInt(glayer.properties.perimeter
                                .replace(
                                    /\D/g,
                                    ''));
                            if (typeof glayer.properties.description != 'undefined')
                                k[x].desc = glayer.properties.description
                            else
                                k[x].desc = "";
                            if (isNaN(k[x].colArea))
                                k[x].colArea = 0;

                            x++;
                        }
                    }
                }
                var result = globalMapManager.groupBy(k, function (item) {
                    return [item.colGroup];
                });
                var grandcolPerim = 0;
                var grandcolArea = 0;

                globalMapManager.generateBlob(result, item.LayerName);
            }
        },
        trySave() {
            globalMapManager.trySave()
        },
        trySaveAs() {
            globalMapManager.trySaveAs()
        },
        editMap: function (file) {

            this.openmapdata.autoid = file.AutoID
            this.openmapdata.build = file.BuildingID
            this.openmapdata.mapid = file.LayerID
            this.openmapdata.mapname = file.LayerName
            this.openmapdata.auto = false;
            this.openMapSaved()


        },
        assignSite: function () {
            if (store.get('map/fileID') == 0) {
                this.snackbar.text = 'Please save the map first'
                this.snackbar.snackbar = true;
                return;
            }
            globalMapManager.showAssignSiteDialog()
        },
        rowExpand: function (data) {

            if (data.length == 0) {
                Vue.set(this, "expandedAuto", [])
            } else {
                var a = _.filter(this.auto, function (n) {
                    return n.LayerID == data[0].LayerID;
                });

                Vue.set(this, "expandedAuto", a);
            }

        },
        openMapSaved: async function () {
            const formBody = new URLSearchParams();
            formBody.append("accessCode", store.get('map/accessCode'));
            formBody.append("LayerID", this.openmapdata.mapid);
            formBody.append("debugData", "map.js => openMapSaved");
            let response = await fetch(myBaseURL + '/node/maps/open-map-layer', {
                method: 'POST',
                body: formBody,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            });

            if (response.ok) {
                let data = await response.text()

                if (data.slice(-6).indexOf('}') == -1) {
                    var result = JSON.parse(data.substring(0, data.length -
                        6));
                    var urlkey = data.slice(-6);
                } else {
                    var urlkey = '';
                    var result = JSON.parse(data);
                }
                store.commit('map/clearData')
                store.commit('baselayer/clearDataAll')
                if (result === Object(result)) {
                    importv1Format(result);
                } else {
                    result = JSON.parse(result);
                    globalMapManager.openv2format(result);
                }
                store.set('map/urlKey', urlkey)
                store.set('map/fileName', this.openmapdata.mapname)
                store.set('map/fileID', this.openmapdata.mapid)
                store.set('map/buildingID', this.openmapdata.build)



                this.openconfirmmapdialog = false;
                this.opendialog = false;
                this.internalFileManager = false;
                this.mapState = "editing";


            } else {
                this.snackbar.text = 'Server is unreachable. Are you connected to the internet?'
                this.snackbar.snackbar = true;
            }
        },
        openMapAuto: async function () {
            alert('openMapAuto');
            const formBody = new URLSearchParams();
            formBody.append("accessCode", store.get('map/accessCode'));
            formBody.append("LayerID", this.openmapdata.mapid);
            formBody.append("AutoID", this.openmapdata.autoid);
            formBody.append("debugData", "map.js => openMapAuto");
            let response = await fetch(myBaseURL + '/node/maps/open-map-layer', {
                method: 'POST',
                body: formBody,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            });

            if (response.ok) {
                let data = await response.text()

                if (data.slice(-6).indexOf('}') == -1) {
                    var result = JSON.parse(data.substring(0, data.length -
                        6));
                    var urlkey = data.slice(-6);
                } else {
                    var urlkey = '';
                    var result = JSON.parse(data);
                }
                store.commit('map/clearData')
                store.commit('baselayer/clearDataAll')
                if (result === Object(result)) {
                    importv1Format(result);
                } else {
                    result = JSON.parse(result);
                    globalMapManager.openv2format(result);
                }
                store.set('map/urlKey', urlkey)
                store.set('map/fileName', this.openmapdata.mapname)
                store.set('map/fileID', this.openmapdata.mapid)
                store.set('map/buildingID', this.openmapdata.build)



                this.openconfirmmapdialog = false;
                this.opendialog = false;
                this.mapState = "editing";


            } else {
                this.snackbar.text = 'Server is unreachable. Are you connected to the internet?'
                this.snackbar.snackbar = true;
            }
        },
        updateData: async function () {
            if (store.get('map/takeoffLink') == false) {
                this.fileManagerloading = true;
                try {
                    const formData = new FormData();
                    formData.append('accessCode', store.get('map/accessCode'));
                    const [layers, sites] = await Promise.all([
                        fetch(myBaseURL + '/node/maps/map-layers', { method: 'POST', body: formData }),
                        fetch(myBaseURL + '/node/sites/get-buildings', { method: 'POST', body: formData })
                    ]);
                    const [layersJson, sitesJson] = await Promise.all([
                        layers.json(),
                        sites.json()
                    ]);

                    let parsedFiles = layersJson.map(function (v) {
                        v.datetime = moment.unix(v.DateTime).format("MM/DD/YY hh:mm a");
                        v.modified = v.DateTime;
                        v.canExpand = true;
                        let search = sitesJson.buildings.find(({ mb_id }) => mb_id == v.BuildingID);
                        v.sitename = typeof search == 'undefined' ? "Unassigned" : search.mb_nickname;
                        v.mb_address1 = typeof search == 'undefined' ? "" : search.mb_address1;
                        v.cityname = typeof search == 'undefined' ? "" : search.cityname;
                        v.statename = typeof search == 'undefined' ? "" : search.statename;
                        return v;
                    });

                    this.buildings = sitesJson.buildings;
                    Vue.set(this, 'files', parsedFiles.filter(file => file.AutoSave == '0'));
                    Vue.set(this, 'auto', parsedFiles.filter(file => file.AutoSave == '1'));

                    // Debug data
                    // console.log("Files:", this.files);
                    // console.log("Auto:", this.auto);

                     // Ensure tabulator is updated
            if (this.$refs.mapTabulatorTable?.tabulator) {
                this.$refs.mapTabulatorTable.tabulator.setData(this.files);
                this.$refs.mapTabulatorTable.tabulator.redraw(true);
            }

            return true; // Add return value for promise chaining
                } catch (error) {
                    console.error("Error loading data:", error);
                } finally {
                    this.fileManagerloading = false;
                }
            }
        },
        extendLeaflet() {
            this.originalMarkerPrototype = Object.assign({}, L.Marker.prototype);
            this.originalIconPrototype = Object.assign({}, L.Icon.prototype);
            this.originalLayerGroupPrototype = Object.assign({}, L.LayerGroup.prototype);
            this.originalPolygonPrototype = Object.assign({}, L.Polygon.prototype);

            L.Polygon.include({

                _clipPoints: function () {
                    // polygons need a different clipping algorithm so we redefine that

                    var bounds = this._renderer._bounds,
                        w = this.options.weight,
                        p = new L.Point(w, w);

                    // increase clip padding by stroke width to avoid stroke on clip edges
                    bounds = new L.Bounds(bounds.min.subtract(p), bounds.max.add(p));

                    this._parts = [];



                    this._parts = this._rings;
                    return;



                }

            });

            L.Marker.include({

                matrix: {
                    1: 0.125,
                    2: 0.125,
                    3: 0.125,
                    4: 0.125,
                    5: 0.125,
                    6: 0.125,
                    7: 0.125,
                    8: 0.125,
                    9: 0.125,
                    10: 0.125,
                    11: 0.125,
                    12: 0.20,
                    13: 0.30,
                    14: 0.40,
                    15: 0.50,
                    16: 0.60,
                    17: 0.70,
                    18: 0.80,
                    19: 0.90,
                    20: 1,
                    21: 1.25,
                    22: 1.5
                },
                onAdd: function (map) {
                    this._zoomAnimated = this._zoomAnimated && map.options.markerZoomAnimation;

                    if (this._zoomAnimated) {
                        map.on('zoomanim', this._animateZoom, this);
                    }

                    map.on('zoomend', this.resize, this);

                    this._initIcon();
                    this.update();
                },
                onRemove: function (map) {
                    if (this.dragging && this.dragging.enabled()) {
                        this.options.draggable = true;
                        this.dragging.removeHooks();
                    }

                    map.off('zoomend', this.resize, this);
                    if (this._zoomAnimated) {
                        map.off('zoomanim', this._animateZoom, this);
                    }

                    this._removeIcon();
                    this._removeShadow();
                },

                scale: function (factor) {

                    var icon = this.options.icon;
                    icon.scale(factor, this._icon);
                },

                resize: function () {

                    this.scale(this.matrix[map.getZoom()]);

                },

            });

            L.Icon.include({

                matrix: {
                    1: 0.125,
                    2: 0.125,
                    3: 0.125,
                    4: 0.125,
                    5: 0.125,
                    6: 0.125,
                    7: 0.125,
                    8: 0.125,
                    9: 0.125,
                    10: 0.125,
                    11: 0.125,
                    12: 0.20,
                    13: 0.30,
                    14: 0.40,
                    15: 0.50,
                    16: 0.60,
                    17: 0.70,
                    18: 0.80,
                    19: 0.90,
                    20: 1,
                    21: 1.25,
                    22: 1.5
                },

                initialize: function (options) {
                    L.setOptions(this, options);


                },

                scale: function (factor, icon) {
                    var options = this.options;

                    if (options.zoom != false) {
                        var to = options.original_size.multiplyBy(factor);
                        var anchorto = options.original_anchor.multiplyBy(factor);
                        if ((to.x !== options.iconSize.x) && (to.y !== options.iconSize.y)) {
                            this.resize(to, anchorto, icon);
                        }
                    }
                },

                resize: function (size, anchor, icon) {
                    //  this.options.iconSize = size;
                    //  this.options.iconAnchor = anchor;
                    this._setIconSize(icon, size, anchor);

                },

                _setIconSize: function (img, size, anchor) {


                    if (anchor && img.tagName != 'DIV') {

                        img.style.marginLeft = (-anchor.x) + 'px';
                        img.style.marginTop = (-anchor.y) + 'px';

                    }


                    if (size && img.tagName != 'DIV') {

                        img.style.width = size.x + 'px';
                        img.style.height = size.y + 'px';

                    }
                },
                _createIcon: function (name, oldIcon) {
                    var src = this._getIconUrl(name);

                    if (!src) {
                        if (name === 'icon') {
                            throw new Error('iconUrl not set in Icon options (see the docs).');
                        }
                        return null;
                    }

                    var img = this._createImg(src, oldIcon && oldIcon.tagName === 'IMG' ? oldIcon : null);

                    this._setIconStyles(img, name);



                    this.scale(this.matrix[map.getZoom()], img);

                    return img;
                },
                _setIconStyles: function (img, name) {
                    var options = this.options;
                    var sizeOption = options[name + 'Size'];

                    if (typeof sizeOption === 'number') {
                        sizeOption = [sizeOption, sizeOption];
                    }

                    var size = L.point(sizeOption),
                        anchor = L.point(name === 'shadow' && options.shadowAnchor || options.iconAnchor ||
                            size && size.divideBy(2, true));
                    options.original_size = L.point(size);
                    options.original_anchor = L.point(anchor);

                    img.className = 'leaflet-marker-' + name + ' ' + (options.className || '');

                    if (anchor) {
                        img.style.marginLeft = (-anchor.x) + 'px';
                        img.style.marginTop = (-anchor.y) + 'px';
                    }

                    if (size) {
                        img.style.width = size.x + 'px';
                        img.style.height = size.y + 'px';
                    }
                }


            });


            L.LayerGroup.include({
                searchLayer: function (id) {
                    for (var i in this._layers) {
                        if (this._layers[i]._leaflet_id == id) {
                            return this._layers[i];
                        }
                        if (this._layers[i]._layers) {
                            for (var j in this._layers[i]._layers) {
                                if (this._layers[i]._layers[j]._leaflet_id == id) {
                                    return this._layers[i]._layers[j];
                                }
                            }
                        }
                    }
                },
                searchLayerParent: function (id) {
                    for (var i in this._layers) {
                        if (this._layers[i]._leaflet_id == id) {
                            return this;
                        }
                        if (this._layers[i]._layers) {
                            for (var j in this._layers[i]._layers) {
                                if (this._layers[i]._layers[j]._leaflet_id == id) {
                                    return this._layers[i];
                                }
                            }
                        }
                    }
                }
            });


            L.LayerGroup.include({

                // @method toGeoJSON(15): Object
                // Returns a [`GeoJSON`](http://en.wikipedia.org/wiki/GeoJSON) representation of the layer group (as a GeoJSON `FeatureCollection`, `GeometryCollection`, or `MultiPoint`).
                // @method toGeoJSON(15): Object
                // Returns a [`GeoJSON`](http://en.wikipedia.org/wiki/GeoJSON) representation of the layer group (as a GeoJSON `GeometryCollection`).
                toGeoJSON: function () {

                    var type = this.feature && this.feature.geometry && this.feature.geometry.type;

                    if (type === 'MultiPoint') {
                        return this.toMultiPoint();
                    }

                    var isGeometryCollection = type === 'GeometryCollection',
                        jsons = [];

                    this.eachLayer(function (layer) {
                        if (layer.toGeoJSON) {
                            var json = layer.toGeoJSON(15);
                            var feature = L.GeoJSON.asFeature(json);

                            if (feature.type == "FeatureCollection" && layer.metadata !== undefined) {
                                feature.metadata = layer.metadata;
                                if (feature.features.length != 0)
                                    jsons.push(isGeometryCollection ? json.geometry : feature);

                            }
                            else {

                                jsons.push(isGeometryCollection ? json.geometry : feature);
                            }

                        }
                    });

                    if (isGeometryCollection) {
                        return L.GeoJSON.getFeature(this, {
                            geometries: jsons,
                            type: 'GeometryCollection'
                        });
                    }

                    return {
                        type: 'FeatureCollection',
                        features: jsons
                    };
                }
            });
        },
        fixDescription: function (value) {
            if (this.selectedItem.feature.properties.type == 'Text')
                return value.replace(/<br[^>]*>/g, "\n").replace(/&nbsp;/g, ' ');
            else
                return value;
        },
        textBackgroundToggle(e) {
            let currentSetting = this.selectedItem.feature.properties?.background ?? false;
            this.selectedItem.feature.properties.background = !currentSetting;
            const iconElement = this.selectedItem.getElement();
            const iconInstance = this.selectedItem.options.icon;
            if (iconElement && iconInstance instanceof CustomDivIcon) {
                if (iconInstance.options.span) {
                    iconInstance.options.background = !currentSetting;
                    const spanElement = iconElement.querySelector('span');
                    if (spanElement) {
                        if (this.selectedItem.feature.properties.background) {
                            spanElement.style.backgroundColor = '#FFFFFF';
                            spanElement.style.border = '1px solid #000000';
                            spanElement.style.padding = '4px';
                            spanElement.style.borderRadius = '4px';
                            spanElement.style.display = 'inline-block';
                        } else {
                            spanElement.style.backgroundColor = 'transparent';
                            spanElement.style.border = 'none';
                            spanElement.style.padding = '4px';
                            spanElement.style.borderRadius = '0px';
                            spanElement.style.display = 'inline-block';

                        }
                    }
                }
            }
        },
        textColorChange(e) {

            if (e) {
                console.log(e)
                //console.log(this.selectedItem)
                this.selectedItem.feature.properties.textColor = e;

                const iconElement = this.selectedItem.getElement();
                const iconInstance = this.selectedItem.options.icon;
                if (iconElement && iconInstance instanceof CustomDivIcon) {
                    if (iconInstance.options.span) {
                        iconInstance.options.textColor = e;
                        const spanElement = iconElement.querySelector('span');
                        if (spanElement) {
                            spanElement.style.color = e;
                        }
                    } else {
                        iconElement.style.color = e;
                    }
                }
            }
        },
        fontSizeChange(e) {

            this.selectedItem.feature.properties.fontSize = e;

            if (typeof this.selectedItem !== "undefined") {
                let tempText = this.selectedItem.feature.properties?.description
                tempText = tempText?.replace(/(?:\r\n|\r|\n)/g, '<br>').replace(/ /g, '&nbsp;');
                if (this.selectedItem.feature.properties.type == 'Text')

                    mostRecent.fontSize = this.selectedItem.feature.properties.fontSize.toString() +
                        'pt';
                var to = [(this.selectedItem.feature.properties.fontSize / 2) * 50 * matrix[map.getZoom()],
                (this.selectedItem.feature.properties
                    .fontSize /
                    2) * 20 * matrix[map.getZoom()]
                ];
                var icon = this.selectedItem.options.icon;
                icon.options.iconSize = to;
                if (typeof this.selectedItem.feature.properties.desctype !== "undefined") {
                    if (selectedItem.feature.properties.desctype == 'none')
                        icon.options.html = tempText
                    else {
                        var obj = JSON.parse(this.selectedItem.feature.properties.description);

                        let temphtml = "<table class='textgrid'>"

                        for (var k in obj) {
                            if (obj.hasOwnProperty(k)) {
                                temphtml += '<tr><td>' + k + '</td><td>' + obj[k] +
                                    '</td></tr>';
                            }
                        }
                        temphtml += "</table>";
                        icon.options.html = temphtml;

                    }
                } else
                    icon.options.html = tempText;


                this.selectedItem.setIcon(icon);
                window.fitText(document.getElementsByClassName('allTextarea'));

            }

        },
        descriptionChange(e) {
            let tempText = e;
            tempText = tempText.replace(/(?:\r\n|\r|\n)/g, '<br>').replace(/ /g, '&nbsp;');

            this.selectedItem.feature.properties.description = e;
            if (typeof this.selectedItem !== "undefined") {
                if (this.selectedItem.feature.properties.type == 'Text') {
                    const iconElement = this.selectedItem.getElement();
                    const iconInstance = this.selectedItem.options.icon;
                    iconInstance.options.html = tempText;
                    this.selectedItem.setIcon(iconInstance);
                    /*this.selectedItem._icon.innerHTML = tempText;
                    let currentSetting = this.selectedItem.feature.properties?.background ?? false;
                    this.selectedItem.feature.properties.background = currentSetting;
                    const iconElement = this.selectedItem.getElement();
                    const iconInstance = this.selectedItem.options.icon;
                    if (iconElement && iconInstance instanceof CustomDivIcon) {
                        if (iconInstance.options.span) {
                            iconInstance.options.background = currentSetting;
                            const spanElement = iconElement.querySelector('span');
                            if (spanElement) {
                                if (this.selectedItem.feature.properties.background) {
                                    spanElement.style.backgroundColor = '#FFFFFF';
                                    spanElement.style.border = '1px solid #000000';
                                    spanElement.style.padding = '4px';
                                    spanElement.style.borderRadius = '4px';
                                    spanElement.style.display = 'inline-block';
                                } else {
                                    spanElement.style.backgroundColor = 'transparent';
                                    spanElement.style.border = 'none';
                                    spanElement.style.padding = '4px';
                                    spanElement.style.borderRadius = '0px';
                                    spanElement.style.display = 'inline-block';

                                }
                            }
                        }
                    }*/
                }
            }
        },
        forceUpdateMapManager() {
            globalMapManager.$forceUpdate();
        },
        addArrow() {
            if (typeof this.selectedItem.feature.properties.arrow != 'undefined')
                if (this.selectedItem.feature.properties.arrow == 'none')
                    globalMapManager.addArrowhead(this.selectedItem, true)
                else
                    globalMapManager.addArrowhead(this.selectedItem, false)
            else
                globalMapManager.addArrowhead(this.selectedItem, true)

            this.$forceUpdate()


        },
        newMap() {
            this.$refs.utilityLib.open("Confirm", "A map is currently open, If you continue all changes in that map will be lost. Do you still want to continue with a new map?", "Yes", "No", true).then(async function (result) {
                if (result) {
                    store.set('baselayer/currentBaseLayer', 2)
                    globalMapManager.shownewMapModel(true);
                    //this.$router.push({name: 'map'})
                }
            }.bind(this));
        },
        changeAnimIcon(marker) {
            this.selectedItem.feature.properties.AnimIcon = marker.iconurl;
        },
        changePhotoMarker(marker) {

            var icon = new L.NumberedDivIcon({
                iconUrl: marker.iconurl,
                iconSize: marker.iconsize,
                number: this.selectedItem.feature.properties.number
            });
            let oldpreset = drawnGroups.searchLayerParent(this.selectedItem._leaflet_id).metadata;
            globalMapManager.removeLayerPreset(oldpreset.shortname, this.selectedItem)
            this.selectedItem.setIcon(icon);

            this.selectedItem.feature.properties.markerURL = this.selectedItem.options.icon.options
                .iconUrl;
            this.selectedItem.feature.properties.markerSize = this.selectedItem.options.icon.options
                .iconSize;
            let obj = photoURL2GroupName(this.selectedItem.feature.properties.markerURL);

            if (obj) {
                let preset = globalMapManager.addPhotoPreset(obj.shortname, obj.name);
                preset.leafletlayer.addLayer(this.selectedItem);
            } else
                globalMapManager.addLayerPreset('photos', this.selectedItem)

            // this is a fix for a bug in leaflet draw that was toggling the edit rectangle on the markers when setting them to editable
            L.DomUtil.addClass(this.selectedItem._icon,
                'leaflet-edit-marker-selected');
            var icon = this.selectedItem._icon;
            var offset = 4;
            var iconMarginTop = parseInt(icon.style.marginTop, 10) - offset,
                iconMarginLeft = parseInt(icon.style.marginLeft, 10) -
                    offset;
            this.selectedItem._icon.style.marginTop = iconMarginTop + 'px';
            this.selectedItem._icon.style.marginLeft = iconMarginLeft + 'px';
        },
        changeMarker(marker) {


            var icon = new LeafletIcon({
                iconUrl: marker.iconurl,
                iconSize: marker.iconsize
            });

            let oldpreset = drawnGroups.searchLayerParent(this.selectedItem._leaflet_id).metadata;
            globalMapManager.removeLayerPreset(oldpreset.shortname, this.selectedItem)

            this.selectedItem.setIcon(icon);
            this.selectedItem.feature.properties.markerURL = this.selectedItem.options.icon.options
                .iconUrl;
            this.selectedItem.feature.properties.markerSize = this.selectedItem.options.icon.options
                .iconSize;
            let obj = iconURL2GroupName(this.selectedItem.feature.properties.markerURL);
            if (obj) {
                let preset = globalMapManager.addMarkerPreset(obj.shortname, obj.name);
                preset.leafletlayer.addLayer(this.selectedItem);
            } else
                globalMapManager.addLayerPreset('markers', this.selectedItem)
            // this is a fix for a bug in leaflet draw that was toggling the edit rectangle on the markers when setting them to editable
            L.DomUtil.addClass(this.selectedItem._icon,
                'leaflet-edit-marker-selected');
            icon = this.selectedItem._icon;
            let offset = 4;
            let iconMarginTop = parseInt(icon.style.marginTop, 10) - offset;
            let iconMarginLeft = parseInt(icon.style.marginLeft, 10) - offset;
            this.selectedItem._icon.style.marginTop = iconMarginTop + 'px';
            this.selectedItem._icon.style.marginLeft = iconMarginLeft + 'px';

        },
        rotate() {
            let rotateBy = this.rotateValue;
            this.stopEditing();
            if (!isNaN(rotateBy)) {
                if (this.selectedItem instanceof L.Polygon) {
                    rotateBy = parseInt(rotateBy)
                    let poly = turf.polygon(L.GeoJSON.latLngsToCoords(this.selectedItem.getLatLngs(), 1, true))
                    let centroid = turf.centroid(poly);
                    let options = {
                        pivot: turf.getCoord(centroid)
                    };
                    let rotatedPoly = turf.transformRotate(poly, rotateBy, options);

                    this.selectedItem.setLatLngs(L.GeoJSON.coordsToLatLngs(turf.getCoords(rotatedPoly), 1));

                }
            }
            this.startEditing(this.selectedItem);
        },
        toggleEditingMode() {
            if (this.selectedItem != null) {
                if (typeof this.selectedItem.pm.options.mydragging == 'undefined') {
                    if (this.selectedItem.pm.enabled())
                        this.stopEditing();
                    try {
                        Vue.set(this.selectedItem.pm.options, "mydragging", true)
                        this.$forceUpdate();
                        this.selectedItem.pm.enableLayerDrag();

                    } catch (err) {

                    }
                }
                else if (this.selectedItem.pm.options.mydragging == false) {
                    if (this.selectedItem.pm.enabled())
                        this.stopEditing();
                    try {
                        Vue.set(this.selectedItem.pm.options, "mydragging", true)
                        this.$forceUpdate();
                        this.selectedItem.pm.enableLayerDrag();

                    } catch (err) {

                    }
                }
                else {
                    try {
                        this.selectedItem.pm.disableLayerDrag();
                        Vue.set(this.selectedItem.pm.options, "mydragging", false)
                        this.$forceUpdate();
                        this.startEditing(this.selectedItem);
                    } catch (err) {
                        console.log(err)

                    }
                }



            }
        },
        fitBounds() {
            Vue.nextTick(function () {
                if (drawnGroups.getBounds().isValid())
                    map.fitBounds(drawnGroups.getBounds());
                else {
                    let myzoomBounds = drawnItems.getBounds();

                    if (myzoomBounds.isValid()) {
                        map.fitBounds(myzoomBounds);
                        if (map.getBoundsZoom(myzoomBounds) > 18)
                            map.setZoom(18);


                    } else {
                        let maxBounds = L.latLngBounds(
                            L.latLng(24.0, -128.23),
                            L.latLng(50.09, -59.14)
                        );
                        map.fitBounds(maxBounds);
                        map.setZoom(4);
                    }
                }
                map.invalidateSize(false);
            }.bind(this))
        },
        changeItemPreset(e) {

            var selectedPreset = e;
            if (selectedPreset != 'none') {
                for (let obj55 in this.selectedItem._eventParents) {

                    this.selectedItem._eventParents[obj55].removeLayer(this.selectedItem);
                }
                globalMapManager.addLayerPreset(selectedPreset, this.selectedItem)


                if (this.selectedItem instanceof L.Polyline && !(this.selectedItem instanceof L.Polygon)) {
                    var shapeOptions = _.omitBy(_.omit(globalMapManager.getPreset(selectedPreset).leafletlayer.metadata, ['fill',
                        'fillColor',
                        'fillOpacity'
                    ]), _.isObject)
                    this.selectedItem.setStyle(shapeOptions);
                } else {
                    this.selectedItem.setStyle(globalMapManager.getPreset(selectedPreset).leafletlayer.metadata);
                }


                //change the values in the stored properties for the item
                var isOk = /(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(
                    'this.selectedItem.options.color');
                if (isOk) {
                    this.selectedItem.feature.properties.color = rgb2hex(this.selectedItem.options.color);
                } else {
                    this.selectedItem.feature.properties.color = this.selectedItem.options.color;
                }
                isOk = /(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(
                    'this.selectedItem.options.fillColor');
                if (isOk) {
                    this.selectedItem.feature.properties.fillColor = rgb2hex(this.selectedItem.options.fillColor);
                } else {
                    this.selectedItem.feature.properties.fillColor = this.selectedItem.options.fillColor;
                }
                var labelvisible = false;

                var legendvisible = false;


                this.selectedItem.feature.properties.fillOpacity = this.selectedItem.options.fillOpacity;
                this.selectedItem.feature.properties.strokeWeight = this.selectedItem.options.weight;
                this.selectedItem.feature.properties.preset = selectedPreset;
                //change the values in the mostRecent object
                mostRecent.preset = selectedPreset;
                mostRecent.fillOpacity = this.selectedItem.options.fillOpacity;
                mostRecent.strokeWeight = this.selectedItem.options.weight;
                mostRecent.color = this.selectedItem.feature.properties.color;
                //	saveLocal();
            } else {

                this.selectedItem.feature.properties.preset = selectedPreset;

            }
        },
        cutRemove() {
            const selectedLayer = this.selectedItem;


            const createFeature = (geom) => ({ type: 'Feature', geometry: geom });
            const createPolygonFeature = (coords) => createFeature({ type: 'Polygon', coordinates: coords });
            const createMultiPolygonFeature = (coords) => createFeature({ type: 'MultiPolygon', coordinates: coords });
            const deepCopyData = (data) => JSON.parse(JSON.stringify(data));

            return new Promise(async (resolve, reject) => {
                for (let groupId in drawnGroups._layers) {
                    if (!drawnGroups._layers.hasOwnProperty(groupId)) continue;

                    const group = drawnGroups._layers[groupId];
                    await this.delay(2);

                    for (let layerId in group._layers) {
                        if (!group._layers.hasOwnProperty(layerId)) continue;

                        const layer = group._layers[layerId];
                        let layerType = layer.feature.properties.type;
                        let fillOpacity = layer.feature.properties.fillOpacity;
                        if (layer === selectedLayer || layer._leaflet_id === selectedLayer._leaflet_id) continue;

                        const layerGeoJSON = layer.toGeoJSON(15);
                        const selectedLayerGeoJSON = selectedLayer.toGeoJSON(15);




                        try {
                            if (!((layerType === 'Rectangle' || layerType === 'Polygon' || layerType == 'MultiPolygon') && fillOpacity > 0)) continue;

                            if (!turf.booleanIntersects(layerGeoJSON, selectedLayerGeoJSON)) continue;
                            const originalProperties = deepCopyData(layer.feature.properties);


                            const difference = polyclip.difference(
                                deepCopyData(layerGeoJSON.geometry.coordinates),
                                deepCopyData(selectedLayerGeoJSON.geometry.coordinates)
                            );


                            if (!difference || difference.length === 0) continue;
                            let geoJsonFeature;
                            if (difference.length == 1)
                                geoJsonFeature = createPolygonFeature(difference[0])
                            else
                                geoJsonFeature = createMultiPolygonFeature(difference);



                            geoJsonFeature.properties = originalProperties;


                            const resultingLayer = L.geoJSON(geoJsonFeature, deepCopyData(layer.options));
                            const firstLayerInResult = resultingLayer._layers[Object.keys(resultingLayer._layers)[0]];

                            if (firstLayerInResult) {
                                firstLayerInResult.on('click', this.onPolyClick);
                                globalMapManager.addLayerPreset(originalProperties.preset, firstLayerInResult);


                                const parentGroup = drawnGroups.searchLayerParent(layer._leaflet_id);
                                if (parentGroup) parentGroup.removeLayer(layer);


                                this.updateLayerProps(firstLayerInResult);
                                globalMapManager.updateLayerInPanel(originalProperties.preset, firstLayerInResult);
                            }
                        } catch (err) {
                            console.error('Error in cutRemove:', err);
                        }
                    }

                }
                const selectedParentGroup = drawnGroups.searchLayerParent(selectedLayer._leaflet_id);
                if (selectedParentGroup) {
                    selectedParentGroup.removeLayer(selectedLayer);
                }
                resolve();
            });
        },

        cutRemove2() {
            let temp = this.selectedItem;
            return new Promise(async function (resolve, reject) {
                for (var preset in drawnGroups._layers) {
                    if (drawnGroups._layers.hasOwnProperty(preset)) {
                        let player = drawnGroups._layers[preset];
                        await this.delay(2);
                        for (var item in player._layers) {
                            if (player._layers.hasOwnProperty(item)) {
                                let layer = player._layers[item];
                                if (((layer.feature.properties.type ==
                                    'Rectangle') || (layer.feature
                                        .properties.type ==
                                        'Polygon')) &&
                                    (layer.feature.properties.fillOpacity >
                                        0)) {
                                    var temp2 = layer;
                                    try {

                                        const diff = turf.difference(turf
                                            .buffer(turf.cleanCoords(temp2
                                                .toGeoJSON(15)), 0), turf
                                                    .buffer(turf.cleanCoords(temp
                                                        .toGeoJSON(15)), 0))
                                        if (diff != null) {
                                            const resultingLayer = L.geoJSON(
                                                diff, temp2.options
                                            );

                                            const foo = resultingLayer._layers[
                                                Object.keys(
                                                    resultingLayer._layers
                                                )[0]];
                                            if (typeof foo != 'undefined') {
                                                foo.on('click', this.onPolyClick);
                                                globalMapManager.addLayerPreset(temp2.feature
                                                    .properties
                                                    .preset, foo)

                                                let layerparent = drawnGroups
                                                    .searchLayerParent(
                                                        temp2._leaflet_id);
                                                if (typeof layerparent !==
                                                    'undefined')
                                                    layerparent.removeLayer(
                                                        temp2);
                                                layerparent = drawnGroups
                                                    .searchLayerParent(
                                                        temp._leaflet_id);
                                                if (typeof layerparent !==
                                                    'undefined')
                                                    layerparent.removeLayer(
                                                        temp);
                                                this.updateLayerProps(foo);
                                                globalMapManager.updateLayerInPanel(
                                                    foo.feature.properties
                                                        .preset,
                                                    foo);

                                            }

                                        }

                                    } catch (err) {
                                        console.log(err);
                                    }

                                }

                            }

                        }
                    }
                }


                resolve();

            }.bind(this));

        },
        cutReplace() {

            let selectedLayer = this.selectedItem;
            const createFeature = (geom) => ({ type: 'Feature', geometry: geom });
            const createPolygonFeature = (coords) => createFeature({ type: 'Polygon', coordinates: coords });
            const createMultiPolygonFeature = (coords) => createFeature({ type: 'MultiPolygon', coordinates: coords });
            const deepCopyData = (data) => JSON.parse(JSON.stringify(data));
            return new Promise(async (resolve, reject) => {
                for (let groupId in drawnGroups._layers) {
                    if (drawnGroups._layers.hasOwnProperty(groupId)) {
                        let group = drawnGroups._layers[groupId];
                        await this.delay(2);

                        for (let layerId in group._layers) {
                            if (group._layers.hasOwnProperty(layerId)) {
                                let layer = group._layers[layerId];
                                let layerType = layer.feature.properties.type;
                                let fillOpacity = layer.feature.properties.fillOpacity;
                                if (layer === selectedLayer || layer._leaflet_id === selectedLayer._leaflet_id) {
                                    continue;
                                }
                                let layerGeoJSON = layer.toGeoJSON(15);
                                let selectedLayerGeoJSON = selectedLayer.toGeoJSON(15);
                                // Check if layer is a rectangle or polygon and has fill opacity
                                if ((layerType === 'Rectangle' || layerType === 'Polygon' || layerType === 'MultiPolygon') && fillOpacity > 0) {

                                    try {
                                        if (!turf.booleanIntersects(layerGeoJSON, selectedLayerGeoJSON)) continue;

                                        let originalProperties = deepCopyData(layer.feature.properties);
                                        let difference = polyclip.difference(
                                            deepCopyData(layerGeoJSON.geometry.coordinates),
                                            deepCopyData(selectedLayerGeoJSON.geometry.coordinates)
                                        );

                                        if (!difference || difference.length === 0) continue;
                                        let geoJsonFeature;
                                        if (difference.length == 1)
                                            geoJsonFeature = createPolygonFeature(difference[0])
                                        else
                                            geoJsonFeature = createMultiPolygonFeature(difference);


                                        geoJsonFeature.properties = originalProperties;
                                        const resultingLayer = L.geoJSON(geoJsonFeature, deepCopyData(layer.options));


                                        let firstLayerInResult = resultingLayer._layers[Object.keys(resultingLayer._layers)[0]];

                                        if (firstLayerInResult) {
                                            firstLayerInResult.on('click', this.onPolyClick);
                                            globalMapManager.addLayerPreset(originalProperties.preset, firstLayerInResult);

                                            let parentGroup = drawnGroups.searchLayerParent(layer._leaflet_id);
                                            if (parentGroup) parentGroup.removeLayer(layer);

                                            this.updateLayerProps(firstLayerInResult);
                                            globalMapManager.updateLayerInPanel(originalProperties.preset, firstLayerInResult);
                                        }

                                    } catch (err) {
                                        console.log(err);
                                    }
                                }
                            }
                        }
                    }
                }
                resolve();
            });
        },


        cutReplace2() {
            let temp = this.selectedItem;
            return new Promise(async function (resolve, reject) {
                for (var preset in drawnGroups._layers) {
                    if (drawnGroups._layers.hasOwnProperty(preset)) {
                        let player = drawnGroups._layers[preset];
                        await this.delay(2);
                        for (var item in player._layers) {
                            if (player._layers.hasOwnProperty(item)) {
                                let layer = player._layers[item];
                                if (((layer.feature.properties.type ==
                                    'Rectangle') || (layer.feature
                                        .properties.type ==
                                        'Polygon')) &&
                                    (layer.feature.properties.fillOpacity >
                                        0)) {

                                    var temp2 = layer;
                                    try {
                                        const diff = turf.difference(turf
                                            .buffer(turf.truncate(temp2
                                                .toGeoJSON(15)), 0), turf
                                                    .buffer(turf.truncate(temp
                                                        .toGeoJSON(15)), 0))
                                        if (diff != null) {

                                            console.log(diff)
                                            console.log(temp
                                                .toGeoJSON(15))
                                            const resultingLayer = L.geoJSON(
                                                diff, temp2.options
                                            );

                                            const foo = resultingLayer._layers[
                                                Object.keys(
                                                    resultingLayer._layers
                                                )[0]];

                                            if (typeof foo != 'undefined') {
                                                foo.on('click', this.onPolyClick);
                                                globalMapManager.addLayerPreset(temp2.feature
                                                    .properties
                                                    .preset, foo)

                                                let layerparent = drawnGroups
                                                    .searchLayerParent(
                                                        temp2._leaflet_id);
                                                if (typeof layerparent !==
                                                    'undefined')
                                                    layerparent.removeLayer(
                                                        temp2);
                                                this.updateLayerProps(foo);
                                                globalMapManager.updateLayerInPanel(
                                                    foo.feature.properties
                                                        .preset,
                                                    foo);

                                            }

                                        }
                                    } catch (err) {
                                        console.log(err);
                                    }

                                }
                            }
                        }
                    }
                }
                resolve();
            }.bind(this));

        },
        async cutHole() {
            this.cutHoleLoading = true;
            let tempselectedItem = this.selectedItem;
            try {
                if (this.cutHoleType == 'cutremove') {
                    this.stopEditing();
                    await this.cutRemove();
                    this.selectedItem = null;
                    this.itemPopup = false;
                    this.cutHoleDialog = false;
                    this.cutHoleType = null;
                    this.cutHoleLoading = false;

                }
                if (this.cutHoleType == 'cutreplace') {
                    this.stopEditing();
                    await this.cutReplace();
                    tempselectedItem.fireEvent('click');
                    this.cutHoleDialog = false;
                    this.cutHoleType = null;
                    this.cutHoleLoading = false;


                }
            } catch (err) {
                console.log(err);
            }

        },
        delay(ms) {
            return new Promise((resolve, reject) => setTimeout(resolve, ms));
        },
        deleteLayerOld(dlayerid) {
            this.$refs.utilityLib.open("Confirm", "Do you want to delete this item?", "Yes", "No", true).then(function (result) {
                if (result) {
                    let layer = drawnGroups.searchLayer(dlayerid);
                    let layerparent = drawnGroups.searchLayerParent(dlayerid);
                    globalMapManager.addPolylineAnimation(layer, false);
                    globalMapManager.addPolylineAnimation(layer, false);
                    layerparent.removeLayer(layer);

                    this.itemDeleteDialog = false;
                    this.snackbar.text = 'Item Deleted.'
                    this.snackbar.snackbar = true;

                    map.fireEvent('click');
                }
            }.bind(this))
        },
        deleteLayer(dlayerid) {
            this.$refs.utilityLib.open("Confirm", "Do you want to delete this item?", "Yes", "No", true).then(function (result) {
                if (result) {
                    let layer = drawnGroups.searchLayer(dlayerid);
                    let layerparent = drawnGroups.searchLayerParent(dlayerid);


                    globalMapManager.addPolylineAnimation(layer, false);


                    if (this.selectedItem === layer) {
                        this.selectedItem = null;
                    }
                    if (this.highlightedLayer === layer) {
                        this.unsetHighlightLayer();
                    }


                    layer.off('click', this.onPolyClick);
                    layer.off('mouseover');
                    layer.off('mouseout');


                    layerparent.removeLayer(layer);


                    this.itemDeleteDialog = false;
                    this.itemPopup = false;
                    this.snackbar.text = 'Item Deleted.';
                    this.snackbar.snackbar = true;
                }
            }.bind(this))
        },
        drawAnother() {


            var currItem = globalMapManager.getPreset(this.selectedItem.feature.properties.preset).leafletlayer.metadata;
            var currItem2 = this.selectedItem.feature.properties

            if (currItem2.preset != '') {
                this.globalSelectedGroup = currItem2.preset;
            }
            if (currItem2.type == 'Polygon') {
                let polygon_options = {
                    templineStyle: currItem,
                    pathOptions: currItem
                }
                if (currItem2.preset != '')
                    this.clone = currItem2.preset;
                map.pm.enableDraw('Polygon', polygon_options);
            }
            if (currItem2.type == 'Rectangle') {
                let polygon_options = {
                    templineStyle: currItem,
                    pathOptions: currItem
                }
                if (currItem2.preset != '')
                    this.clone = currItem2.preset;
                map.pm.enableDraw('Rectangle', polygon_options);
            }
            if (currItem2.type == 'Circle') {
                let polygon_options = {
                    templineStyle: currItem,
                    pathOptions: currItem
                }
                if (currItem2.preset != '')
                    this.clone = currItem2.preset;
                map.pm.enableDraw('Circle', polygon_options);
            }
            if (currItem2.type == 'Line') {
                let polygon_options = {
                    templineStyle: _.omitBy(_.omit(currItem, ['fill', 'fillColor', 'fillOpacity']), _.isObject),
                    pathOptions: _.omitBy(_.omit(currItem, ['fill', 'fillColor', 'fillOpacity']), _.isObject)
                }
                if (currItem2.preset != '')
                    this.clone = currItem2.preset;
                map.pm.enableDraw('Line', polygon_options);
            }
            if (currItem2.type == 'Marker') {
                var MyCustomMarker = L.Icon.extend({
                    options: {

                        iconSize: currItem2.markerSize,
                        iconUrl: currItem2.markerURL
                    }
                });
                let polygon_options = {
                    icon: new MyCustomMarker()
                }
                map.pm.Draw.Marker.options.markerStyle = {
                    icon: new MyCustomMarker()
                }
                map.pm.enableDraw('Marker');

            }
        },
        copyLayer(clayerid) {

            this.stopEditing();
            let mylayer = drawnGroups.searchLayer(clayerid);

            let options = _.cloneDeep(mylayer.options);
            let layerparent = drawnGroups.searchLayerParent(clayerid);

            let newlayer;

            if (mylayer instanceof L.Marker && mylayer.feature.properties.type != "Text") {
                var point = turf.point(L.GeoJSON.latLngToCoords(mylayer.getLatLng()));
                var translatedpoint = turf.transformTranslate(point, 0.01, 45);
                newlayer = L.marker(L.GeoJSON.coordsToLatLng(turf.getCoord(translatedpoint)), options);
            } else if (mylayer instanceof L.Circle) {
                var point = turf.point(L.GeoJSON.latLngToCoords(mylayer.getLatLng()));
                var translatedpoint = turf.transformTranslate(point, 0.01, 45);
                newlayer = L.circle(L.GeoJSON.coordsToLatLng(turf.getCoord(translatedpoint)), mylayer.getRadius(), options);
            } else if (mylayer instanceof L.CircleMarker) {
                var point = turf.point(L.GeoJSON.latLngToCoords(mylayer.getLatLng()));
                var translatedpoint = turf.transformTranslate(point, 0.01, 45);
                newlayer = L.circleMarker(L.GeoJSON.coordsToLatLng(turf.getCoord(translatedpoint)), options);
            } else if (mylayer instanceof L.Rectangle) {
                var poly = turf.polygon(L.GeoJSON.latLngsToCoords(mylayer.getLatLngs(), 1, true))
                var translatedpoly = turf.transformTranslate(poly, 0.01, 45);
                var bbox = turf.bbox(translatedpoly);
                var corner1 = L.latLng(bbox[1], bbox[0]);
                var corner2 = L.latLng(bbox[3], bbox[2]);
                var bounds = L.latLngBounds(corner1, corner2);
                newlayer = L.rectangle(bounds, options);
            } else if (mylayer instanceof L.Polygon) {
                var poly = turf.polygon(L.GeoJSON.latLngsToCoords(mylayer.getLatLngs(), 1, true))
                var translatedpoly = turf.transformTranslate(poly, 0.01, 45);
                newlayer = L.polygon(L.GeoJSON.coordsToLatLngs(turf.getCoords(translatedpoly), 1), options);
            } else if (mylayer instanceof L.Polyline) {
                var line = turf.lineString(L.GeoJSON.latLngsToCoords(mylayer.getLatLngs()));
                var translatedline = turf.transformTranslate(line, 0.01, 45);
                newlayer = L.polyline(L.GeoJSON.coordsToLatLngs(turf.getCoords(translatedline)), options);
            } else if (mylayer.feature.properties.type == "Text") {
                var point = turf.point(L.GeoJSON.latLngToCoords(mylayer.getLatLng()));
                var translatedpoint = turf.transformTranslate(point, 0.01, 45);
                var tpoint = L.GeoJSON.coordsToLatLng(turf.getCoord(translatedpoint))
                this.addNewTextArea(mylayer.feature.properties.description, tpoint, mylayer.feature.properties.fontSize - 7, mylayer.feature.properties?.textColor ?? '#FFFFFF');
                console.log(mylayer.feature.properties)
                return;
            }

            var feature = newlayer.feature = newlayer.feature || {};
            feature.type = "Feature";
            feature.properties = feature.properties || {};
            this.shapeCounter += 1;
            feature.properties["id"] = this.shapeCounter;
            feature.properties["name"] = '';
            feature.properties["preset"] = mylayer.feature.properties["preset"];
            this.updateLayerProps(newlayer);

            newlayer.on('click', this.onPolyClick);
            globalMapManager.addLayerPreset(layerparent.metadata.shortname, newlayer)

            newlayer.fireEvent('click');
            Vue.nextTick(function () {
                this.toggleEditingMode()
            }.bind(this));



        },

        toggleEstimator: function () {
            this.estimator = !this.estimator;
            Vue.nextTick(function () {
                this.onResize()
            }.bind(this));
        },
        openFileManager() {
            this.$router.push({ name: 'fileman', params: { 'back': true } })
        },
        saveMap() {
            globalMapManager.trySave();
        },
        saveMapAs() {
            globalMapManager.trySaveAs();
        },
        getCenterofShape(mylayer) {
            let latlngCenter;
            if (mylayer.feature.properties.type != "Circle" && mylayer.feature.properties.type != "Marker" && mylayer.feature.properties.type != "Photo" && mylayer.feature.properties.type != "Text") {
                let bounds = mylayer.getBounds();
                latlngCenter = bounds.getCenter();
            } else {
                latlngCenter = mylayer._latlng;
            }
            return latlngCenter;
        },
        unsetHighlightLayer() {
            if (this.highlightedLayer != null) {
                if (this.highlightedLayer.feature.properties.type == "Marker") {
                    this.highlightedLayer._icon.style.borderRadius = "0px";
                    this.highlightedLayer._icon.style.backgroundColor = "";
                } else
                    this.highlightedLayer._path.setAttribute("filter", "");
            }
            this.highlightedLayer = null;
        },
        setHighlightLayer(layer) {
            // Check if something's highlighted, if so unset highlight
            if (this.highlightedLayer) {
                this.unsetHighlightLayer();
            }
            if (layer.feature.properties.type == "Marker") {
                layer._icon.style.borderRadius = "50%";
                layer._icon.style.backgroundColor = "white";
            } else {
                if (!isSafari)
                    layer._path.setAttribute("filter", "url(#outline_selected)");
            }
            this.highlightedLayer = layer;
        },
        calcItemPopupPosition(target, popup) {
            let fWidth = popup.offsetWidth
            let fHeight = popup.offsetHeight;
            let XOff = 12;
            let YOff = 25;
            var latlngCenter = this.getCenterofShape(target);
            var centerPos = map.latLngToContainerPoint(latlngCenter);

            if (centerPos.x <= fWidth / 2) {
                if (centerPos.x < 0) {

                    centerPos.x = map.getSize().x / 2;
                } else {

                    centerPos.x = centerPos.x + (fWidth / 2);
                }
            } else if (centerPos.x >= map.getSize().x - fWidth / 2) {

                if (centerPos.x > map.getSize().x) {
                    centerPos.x = map.getSize().x / 2;
                } else {
                    centerPos.x = centerPos.x - fWidth / 2;
                }
            }
            if (centerPos.y < 0) {
                centerPos.y = map.getSize().y / 2;
            } else if (centerPos.y > map.getSize().y) {
                centerPos.y = map.getSize().y / 2;
            }
            if (centerPos.y + fHeight > map.getSize().y && centerPos.y + fHeight > map.getSize().y / 2) {
                centerPos.y = centerPos.y + (map.getSize().y - (centerPos.y + fHeight));
                centerPos.y = centerPos.y - 50;
            }

            centerPos.x = centerPos.x - XOff;
            if (centerPos.y > ((map.getSize().y) / 2)) {
                centerPos.y = centerPos.y - YOff;
                this.itemPropStyle.left = centerPos.x - fWidth / 2 + 'px';
                this.itemPropStyle.top = ((centerPos.y - fHeight) + 57) + 'px';
                this.itemPropClass = ['no-pseudo-before']

            } else {
                this.itemPropStyle.left = centerPos.x - fWidth / 2 + 'px';
                this.itemPropStyle.top = (centerPos.y + 57) + 'px';
                this.itemPropClass = ['no-pseudo-after']
            }

        },
        removeItemPopup() {
            this.itemPopup = false;
        },
        onPolyClick(event) {
            if (!this.drawing) {
                if (this.selectedItem != null) {

                    if (this.selectedItem == event.target) {





                        if (typeof this.selectedItem.pm.options.mydragging != 'undefined')
                            if (this.selectedItem.pm.options.mydragging) {
                                this.itemPopup = true
                                this.unsetHighlightLayer();
                                Vue.nextTick(function () {
                                    this.calcItemPopupPosition(this.selectedItem, this.$refs.itemPopup.$el)
                                }.bind(this));
                                L.DomEvent.stopPropagation(event);
                                return;
                            }
                    }
                }
                this.selectedItem = event.target;
                if (this.selectedItem.feature.properties.type == 'Line') {
                    let props = this.selectedItem.feature.properties;

                    if (typeof props.anim == "undefined") {
                        props.anim = "false";
                        props.AnimIcon = myBaseURL + "/images/snowplow1.svg";
                    } else {

                    }
                    if (typeof props.AnimIcon == "undefined") {
                        props.AnimIcon = myBaseURL + "/images/snowplow1.svg";

                    }
                }

                this.itemPopup = true
                this.unsetHighlightLayer();
                Vue.nextTick(function () {
                    this.calcItemPopupPosition(this.selectedItem, this.$refs.itemPopup.$el)
                }.bind(this));
                if (this.selectedItem.feature.properties != 'Text') {
                    this.stopEditing();
                    this.startEditing(this.selectedItem);
                    L.DomEvent.stopPropagation(event);
                }
            }

        },
        addLayerToMap(layer) {

            if (layer instanceof L.Marker) {
                map.pm.Draw.Marker.options.markerStyle = {
                    icon: LorangeIcon
                }
                let obj = iconURL2GroupName(layer.feature.properties.markerURL);
                if (obj) {
                    let preset = globalMapManager.addMarkerPreset(obj.shortname, obj.name);
                    globalMapManager.addLayerPreset(obj.shortname, layer)
                    preset.leafletlayer.addLayer(layer);
                } else
                    globalMapManager.addLayerPreset("markers", layer)
            } else {


                globalMapManager.addLayerPreset(this.globalSelectedGroup, layer)

            }
        },


        stopEditing() {
            // if a layer is being edited, finish up and disable editing on it afterward.

            if (this.currentlyEditing) {

                if (this.currentlyEditing.feature.properties.type == "Photo" || this.currentlyEditing.feature.properties.type ==
                    "Marker") {
                    if (this.currentlyEditing._icon) {
                        L.DomUtil.removeClass(this.currentlyEditing._icon, 'leaflet-edit-marker-selected');
                        let icon = this.currentlyEditing._icon;
                        let offset = 4;
                        let iconMarginTop = parseInt(icon.style.marginTop, 10) + offset;
                        let iconMarginLeft = parseInt(icon.style.marginLeft, 10) + offset;
                        this.currentlyEditing._icon.style.marginTop = iconMarginTop + 'px';
                        this.currentlyEditing._icon.style.marginLeft = iconMarginLeft + 'px';
                    }
                }
                this.updateLayerProps(this.currentlyEditing);
                //currentlyEditing.disableEdit();
                if (this.currentlyEditing.pm.enabled())
                    this.currentlyEditing.pm.disable();
                //this.currentlyEditing.pm.disableLayerDrag();
                this.currentlyEditing = null;


            }


        },
        startEditing(layer) {
            if (!this.disableEditing) {
                if (layer.feature.properties.type == "Photo" || layer.feature.properties.type == "Marker") {
                    L.DomUtil.addClass(layer._icon, 'leaflet-edit-marker-selected');
                    let icon = layer._icon;
                    let offset = 4;
                    let iconMarginTop = parseInt(icon.style.marginTop, 10) - offset;
                    let iconMarginLeft = parseInt(icon.style.marginLeft, 10) - offset;
                    layer._icon.style.marginTop = iconMarginTop + 'px';
                    layer._icon.style.marginLeft = iconMarginLeft + 'px';
                }
                layer.pm.enable({
                    allowSelfIntersection: true,
                    limitMarkersToCount: 40
                });
                layer.on("pm:edit", function (event) {
                    var layer = event.target,
                        content = null;

                    this.updateLayerProps(layer);

                }.bind(this));
                layer.on("pm:markerdragend", function (event) {


                    var layers = event.target;

                    this.lastdragevent = Date.now();
                    let evtparent = drawnGroups.searchLayerParent(event.target._leaflet_id)
                    if (typeof evtparent.metadata != 'undefined') {
                        this.updateLayerProps(event.target);
                        globalMapManager.updateLayerInPanel(evtparent.metadata.shortname, event.target);
                    }


                }.bind(this));


                this.currentlyEditing = layer;
            }
        },
        contextClick(e) {
            switch (e.el.innerText) {
                case 'Center map here':
                    map.panTo(e.latlng);
                    break;
                case 'Zoom in':
                    map.zoomIn();
                    break;
                case 'Zoom out':
                    map.zoomOut();
                    break;

                default:
                    break;
            }
        },
        initializeMap() {
            map = L.map('map', {
                editable: true,
                center: [51.505, -0.09],
                zoom: 13,
                preferCanvas: false,
                contextmenu: false,
                contextmenuWidth: 140,
                contextmenuItems: [{
                    text: 'Center map here',
                    callback: this.contextClick,
                }, {
                    text: 'Zoom in',
                    callback: this.contextClick
                }, {
                    text: 'Zoom out',
                    callback: this.contextClick
                }]
            });
            map.addLayer(globalGoogleSatLayer)

            map.doubleClickZoom.disable();

            // Add pegman button for street view
            this.addPegmanButton();

            map.on('click', function (e) {
                var millis = Date.now() - this.lastdragevent;

                if (millis > 60) {
                    this.stopEditing();
                    this.itemPopup = false;

                }


            }.bind(this));
            map.on('movestart', function (e) {
            });
            map.on('moveend', function (e) {
                let mapCenter = map.getCenter();
                if (typeof this.loaded != 'undefined') {
                    if (this.loaded) {
                        if (this.loadedtype == "gmap") {
                            this.disableeventlistner = true;
                            try {
                                this.loadedvar.setCenter(mapCenter);
                            } catch (err) {
                                console.log(err);
                            }
                            this.disableeventlistner = false;
                        }
                        if (this.loadedtype == "bbmap") {
                            this.disableeventlistner = true;
                            try {
                                this.loadedvar.setView({
                                    center: new Microsoft.Maps.Location(mapCenter.lat,
                                        mapCenter.lng)
                                });
                            } catch (err) {
                                console.log(err);
                            }
                            this.disableeventlistner = false;
                        }
                    }
                }
            });

            map.on('load', function () {

            });


            map.on('pm:create', function (event) {
                var layer = event.layer,
                    layerType = event.layerType;

                var feature = layer.feature = layer.feature || {};

                if (event.shape == "CircleMarker") {

                    layer.setRadius(0.4572);

                }
                if (this.globalSelectedGroup == "none" && !(layer instanceof L.Marker)) {
                    globalMapManager.chooseDefaultGroup().then(
                        function () {
                            feature.type = "Feature";
                            feature.properties = feature.properties || {};
                            this.shapeCounter += 1;
                            feature.properties["id"] = this.shapeCounter;
                            feature.properties["name"] = '';
                            feature.properties["preset"] = this.globalSelectedGroup;

                            if (this.clone != '') {
                                feature.properties["preset"] = this.clone;
                                this.clone = '';
                            }


                            this.updateLayerProps(layer);

                            layer.on('click', this.onPolyClick);
                            if (layer instanceof L.Polyline && !(layer instanceof L.Polygon)) {
                                let shapeOptions = _.omitBy(_.omit(globalMapManager.getPreset(this.globalSelectedGroup).leafletlayer.metadata, [
                                    'fill', 'fillColor',
                                    'fillOpacity'
                                ]), _.isObject)
                                layer.setStyle(shapeOptions);
                            } else {
                                layer.setStyle(
                                    globalMapManager.getPreset(this.globalSelectedGroup).leafletlayer.metadata
                                );
                            }
                            if (this.continuousmodeFlag) {
                                this.addLayerToMap(layer);
                                layer.pm.disable();
                                // map.pm.Draw.enable();
                            } else {
                                map.pm.Draw.disable();
                                this.addLayerToMap(layer);
                                layer.fireEvent('click');
                            }

                        }.bind(this)
                    );
                } else {
                    feature.type = "Feature";
                    feature.properties = feature.properties || {};
                    this.shapeCounter += 1;
                    feature.properties["id"] = this.shapeCounter;
                    feature.properties["name"] = '';
                    feature.properties["preset"] = this.globalSelectedGroup;
                    if (!(layer instanceof L.Marker)) {
                        if (layer instanceof L.Polyline && !(layer instanceof L.Polygon)) {
                            let shapeOptions = _.omitBy(_.omit(globalMapManager.getPreset(this.globalSelectedGroup).leafletlayer.metadata, ['fill',
                                'fillColor',
                                'fillOpacity'
                            ]), _.isObject)
                            layer.setStyle(shapeOptions);
                        } else {
                            layer.setStyle(
                                globalMapManager.getPreset(this.globalSelectedGroup).leafletlayer.metadata
                            );
                        }
                    }

                    if (this.clone != '') {
                        feature.properties["preset"] = this.clone;
                        this.clone = '';
                    }
                    if (this.continuousmodeFlag) {
                        this.updateLayerProps(layer);
                        layer.on('click', this.onPolyClick);
                        this.addLayerToMap(layer);
                        layer.pm.disable();
                    } else {
                        map.pm.Draw.disable();
                        this.updateLayerProps(layer);
                        layer.on('click', this.onPolyClick);
                        this.addLayerToMap(layer);
                        layer.fireEvent('click');
                    }

                }

            }.bind(this));


            map.on('pm:drawstart', function (event, targetlayer) {
                this.drawing = true;

                globalMapManager.drawStart(event);
            }.bind(this));

            map.on('pm:drawend', function (event, targetlayer) {
                this.drawing = false;


            }.bind(this));


            map.on('zoomend', function () {
                var currentZoom = map.getZoom();
                var labels = document.getElementsByClassName('allLabels');
                for (let i = 0; i < labels.length; i++) {
                    labels[i].style.fontSize = currentZoom - 8;
                }

                if (typeof globalMapManager.getPreset("textinternal") !== 'undefined') {
                    if (typeof globalMapManager.getPreset("textinternal").leafletlayer !== 'undefined') {

                        globalMapManager.getPreset("textinternal").leafletlayer.eachLayer(function (layer) {
                            if (layer.feature.properties.type == "Text") {
                                var zoom = map.getZoom();
                                var icon = layer.options.icon;
                                var orignal_size = icon.options.iconSize;
                                var orignal_anchor = icon.options.iconAnchor
                                if (layer.feature.properties.fontSize - (22 - zoom) <= 0) {
                                    if (layer._icon != null)
                                        layer._icon.style.visibility = 'hidden'
                                    //$(layer._icon).hide();
                                } else {
                                    if (layer._icon != null)
                                        layer._icon.style.visibility = 'visible'
                                    // $(layer._icon).show();
                                }
                                var to = [(layer.feature.properties.fontSize / 2) * 50 * matrix[map
                                    .getZoom()],
                                (layer.feature.properties.fontSize /
                                    2) * 20 *
                                matrix[map.getZoom()]
                                ];
                                icon.options.iconSize = to;
                                let tempText = layer.feature.properties?.description
                                tempText = tempText?.replace(/(?:\r\n|\r|\n)/g, '<br>').replace(/ /g, '&nbsp;');
                                if (typeof layer.feature.properties.desctype !== "undefined") {
                                    if (layer.feature.properties.desctype == 'none')
                                        icon.options.html = tempText
                                    else {
                                        var obj = JSON.parse(layer.feature.properties.description);

                                        let temphtml = "<table class='textgrid'>"

                                        for (var k in obj) {
                                            if (obj.hasOwnProperty(k)) {
                                                temphtml += '<tr><td>' + k + '</td><td>' + obj[k] +
                                                    '</td></tr>';
                                            }
                                        }
                                        temphtml += "</table>";
                                        icon.options.html = temphtml;

                                    }
                                } else
                                    icon.options.html = tempText

                                layer.setIcon(icon);
                                window.fitText(document.getElementsByClassName('allTextarea'));

                            }
                        });
                    }

                }
            });
            layerGroup.addTo(map);

            map.pm.setGlobalOptions({ snappable: false });
            L.DomEvent.addListener(map, 'addtextarea', this.addNewTextArea2);
            L.DomEvent.addListener(map,
                'continuousmode', this.continuousmode);
            L.DomEvent.addListener(map, 'addedges', this.drawEdges);
            map.on("pm:markerdragend", function (event) {
                event.stopPropagation();
                var layers = event.layer,
                    content = null;
                //	updateLayerProps(layers);
                //	saveLocal();
                this.lastdragevent = Date.now();
                for (let obj in event.layer._eventParents) {
                    if (event.layer._eventParents[obj].metadata !== undefined)
                        globalMapManager.updateLayerInPanel(event.layer._eventParents[obj].metadata.shortname, event.layer);
                }

            }.bind(this));





            map.pm.addControls({
                position: 'topleft',
                editMode: false,
                dragMode: false,
                cutPolygon: false,
                removalMode: false,

            });
            map.pm.Draw.Marker.options.markerStyle = {
                icon: LorangeIcon
            }
            map.addControl(new globalLegendControl.LegendControl());

        },
        updateLayerProps(layer) {

            if (layer instanceof L.Marker) {
                if (layer.feature.properties.type == 'Photo') {
                    layer.feature.properties.type = 'Photo';
                    layer.feature.properties.location = layer._latlng;
                    layer.feature.properties.markerURL = layer.options.icon.options.iconUrl;
                    layer.feature.properties.markerSize = layer.options.icon.options.iconSize;
                } else {
                    if (typeof layer.feature.properties.type == 'undefined') {
                        layer.feature.properties.type = 'Marker';
                        layer.feature.properties.location = layer._latlng;
                        layer.feature.properties.markerSize = layer.options.icon.options.iconSize;
                        layer.feature.properties.markerURL = layer.options.icon.options.iconUrl;
                        if (layer.feature.properties.name == '')
                            layer.feature.properties.name = 'Marker-' + layer.feature.properties.id;
                    } else if (layer.feature.properties.type == 'Marker') {
                        layer.feature.properties.type = 'Marker';
                        layer.feature.properties.location = layer._latlng;
                        layer.feature.properties.markerSize = layer.options.icon.options.iconSize;
                        layer.feature.properties.markerURL = layer.options.icon.options.iconUrl;
                        if (layer.feature.properties.name == '')
                            layer.feature.properties.name = 'Marker-' + layer.feature.properties.id;
                    }
                }
            } else if (layer instanceof L.Circle || layer instanceof L.CircleMarker) {

                let center = layer.getLatLng(),
                    rad = layer.getRadius();
                let radius = rad * 3.28084;
                area = radius * radius * Math.PI;
                distance = (radius + radius) * Math.PI;
                distance = numeral(_round(distance, 2)).format('0,0');
                if (layer.feature.properties.name == '')
                    layer.feature.properties.name = 'Circle-' + layer.feature.properties.id;
                //subtractThis = getSubtractAreas(layer.feature.properties.name);
                //area = area - subtractThis;
                area = numeral(Math.round(area)).format('0,0');
                layer.feature.properties.type = 'Circle';
                layer.feature.properties.radius = rad;
                layer.feature.properties.color = layer.options.color;
                layer.feature.properties.fillColor = layer.options.fillColor;
                layer.feature.properties.fillOpacity = layer.options.fillOpacity;
                layer.feature.properties.stroke = layer.options.stroke;
                layer.feature.properties.strokeWeight = layer.options.weight;
                layer.feature.properties.area = area + ' sqft';
                layer.feature.properties.perimeter = distance + ' ft';
                layer.on('mouseover', function (e) {
                    this.setHighlightLayer(layer);
                }.bind(this));
                layer.on('mouseout', function (e) {
                    this.unsetHighlightLayer(layer);
                }.bind(this));
            } else if (layer instanceof L.Rectangle) {
                //RECTANGLE
                layer.feature.properties.type = 'Rectangle';
                layer.feature.properties.color = layer.options.color;
                layer.feature.properties.fillColor = layer.options.fillColor;
                layer.feature.properties.fillOpacity = layer.options.fillOpacity;
                layer.feature.properties.stroke = layer.options.stroke;
                layer.feature.properties.strokeWeight = layer.options.weight;
                var latlngs = layer._defaultShape ? layer._defaultShape() : layer.getLatLngs(),
                    distance = 0,

                    area = Math.round(turf.area(layer.toGeoJSON(15)) *
                        10.76391041671)
                if (layer.feature.properties.name == '')
                    layer.feature.properties.name = 'Rect-' + layer.feature.properties.id;
                var subtractThis = this.getSubtractAreas(layer.feature.properties.name);
                area = area - subtractThis;
                area = numeral(area).format('0,0');
                layer.feature.properties.area = area + ' sqft';
                if (latlngs.length > 2) {
                    for (var i = 0; i < latlngs.length - 1; i++) {
                        distance += latlngs[i].distanceTo(latlngs[i + 1]);
                    }
                    distance += latlngs[latlngs.length - 1].distanceTo(latlngs[0]);
                    distance = distance * 3.28084;
                    distance = numeral(_round(distance, 2)).format('0,0');
                }
                layer.feature.properties.perimeter = distance + ' ft';
                layer.on('mouseover', function (e) {
                    this.setHighlightLayer(layer);

                }.bind(this));
                layer.on('mouseout', function (e) {
                    this.unsetHighlightLayer(layer);
                }.bind(this));

            } else if (layer instanceof L.Polygon) {
                layer.feature.properties.type = 'Polygon';
                layer.feature.properties.color = layer.options.color;
                layer.feature.properties.fillColor = layer.options.fillColor;
                layer.feature.properties.fillOpacity = layer.options.fillOpacity;
                layer.feature.properties.stroke = layer.options.stroke;
                layer.feature.properties.strokeWeight = layer.options.weight;
                latlngs = layer._defaultShape ? layer._defaultShape() : layer.getLatLngs();
                distance = 0;
                area = Math.round(turf.area(layer.toGeoJSON(15)) *
                    10.76391041671);

                if (layer.feature.properties.name == '')
                    layer.feature.properties.name = 'Poly-' + layer.feature.properties.id;
                //subtractThis = getSubtractAreas(layer.feature.properties.name);
                //area = area - subtractThis;
                area = numeral(area).format('0,0');
                layer.feature.properties.area = area + ' sqft';
                if (latlngs.length > 2) {
                    for (i = 0; i < latlngs.length - 1; i++) {
                        distance += latlngs[i].distanceTo(latlngs[i + 1]);
                    }
                    distance += latlngs[latlngs.length - 1].distanceTo(latlngs[0]);
                    distance = distance * 3.28084;
                    distance = numeral(_round(distance, 2)).format('0,0');
                }
                layer.feature.properties.perimeter = distance + ' ft';
                layer.on('mouseover', function (e) {
                    this.setHighlightLayer(layer);

                }.bind(this));
                layer.on('mouseout', function (e) {
                    this.unsetHighlightLayer(layer);
                }.bind(this));

            } else if (layer instanceof L.Polyline) {
                layer.feature.properties.type = 'Line';
                layer.feature.properties.color = layer.options.color;
                layer.feature.properties.fillColor = layer.options.fillColor;
                layer.feature.properties.stroke = layer.options.stroke;
                layer.feature.properties.strokeWeight = layer.options.weight;
                latlngs = layer._defaultShape ? layer._defaultShape() : layer.getLatLngs();
                distance = 0;
                if (latlngs.length < 2) {
                    return "Distance: N/A";
                } else {
                    for (i = 0; i < latlngs.length - 1; i++) {
                        distance += latlngs[i].distanceTo(latlngs[i + 1]);
                    }
                    distance = distance * 3.28084;
                    distance = numeral(_round(distance, 2)).format('0,0');
                }
                layer.feature.properties.perimeter = distance + ' ft';
                if (layer.feature.properties.name == '')
                    layer.feature.properties.name = 'Line-' + layer.feature.properties.id;
                layer.on('mouseover', function (e) {
                    this.setHighlightLayer(layer);

                }.bind(this));
                layer.on('mouseout', function (e) {
                    this.unsetHighlightLayer(layer);
                }.bind(this));

            }

        },
        getSubtractAreas(shapeName) {
            var subtractArea = 0;
            drawnGroups.eachLayer(function (player) {
                player.eachLayer(function (layer) {
                    if (typeof layer.feature.properties.subtract !== "undefined") {
                        if ((layer.feature.properties.subtract == shapeName) && (shapeName.length > 0)) {
                            if (layer.feature.properties.area) {
                                var areaInCell = layer.feature.properties.area.substr(0, (layer.feature
                                    .properties
                                    .area
                                    .length -
                                    5));
                                subtractArea = subtractArea + parseInt(areaInCell.replace(/,/g, ''), 10);
                            }
                        }
                    }
                });
            });
            return subtractArea;
        },
        addNewTextArea(text = 'Click to Edit Text<br>Drag to Move Text', location = map.getCenter(),
            fontSize = 9, color = '#FFFFFF', background = false) {

            fontSize = Math.round(fontSize + ((22 - map.getZoom()) * 1 / matrix[map.getZoom()]));
            var k = [(fontSize / 2) * 50 * matrix[map.getZoom()], (fontSize / 2) * 20 * matrix[map.getZoom()]];
            var label = new L.Marker(location, {
                icon: new CustomDivIcon({
                    className: 'allTextarea',
                    html: text,
                    iconSize: k,
                    span: true,
                    textColor: color,
                    background: background,
                    spanClassName: 'allTextAreaSpan',
                    iconAnchor: [0, 0],
                }),
                draggable: 'true'
            });
            var layer = label;
            var feature = layer.feature = layer.feature || {};
            feature.type = "Feature";
            feature.properties = feature.properties || {};
            feature.properties["type"] = 'Text';
            feature.properties["fontSize"] = fontSize;
            feature.properties["description"] = text;
            feature.properties["textColor"] = color;
            feature.properties["background"] = background;
            this.shapeCounter += 1;
            layer.feature.properties.id = this.shapeCounter;
            layer.feature.properties.name = "Text-" + this.shapeCounter;

            globalMapManager.addLayerPreset('textinternal', layer)

            layer.on('click', this.onPolyClick);
            //END TEXT LABELS
            window.fitText(document.getElementsByClassName('allTextarea'));
        },
        addNewTextArea2() {



            var text = 'Click to Edit Text<br>Drag to Move Text';
            var location = map.getCenter();
            var fontSize = 9;
            fontSize = Math.round(fontSize + ((22 - map.getZoom()) * 1 / matrix[map.getZoom()]));
            var k = [(fontSize / 2) * 50 * matrix[map.getZoom()], (fontSize / 2) * 20 * matrix[map.getZoom()]];
            var label = new L.Marker(location, {
                icon: new CustomDivIcon({
                    className: 'allTextarea',
                    html: text,
                    iconSize: k,
                    span: true,
                    textColor: '#FFFFFF',
                    background: false,
                    spanClassName: 'allTextAreaSpan',

                    iconAnchor: [0, 0],
                }),
                draggable: 'true'
            });
            var layer = label;
            var feature = layer.feature = layer.feature || {};
            feature.type = "Feature";
            feature.properties = feature.properties || {};
            feature.properties["type"] = 'Text';
            feature.properties["fontSize"] = fontSize;
            feature.properties["description"] = text;
            feature.properties["textColor"] = '#FFFFFF';
            feature.properties["background"] = false;
            this.shapeCounter++;
            layer.feature.properties.id = this.shapeCounter;
            layer.feature.properties.name = "Text-" + this.shapeCounter;
            globalMapManager.addLayerPreset('textinternal', layer)

            layer.on('click', this.onPolyClick);
            window.fitText(document.getElementsByClassName('allTextarea'));
        },

        continuousmode() {
            map.pm.Draw.disable();
            this.continuousmodeFlag = !this.continuousmodeFlag;
        },
        openSettings() {

            this.$root._router.push({ name: 'account' })
            //this.$router.push({name: 'map'})
        },
        logout() {
            window.location = myBaseURL + '/index/logout'
        },


        onResize: _.debounce(function (args) {

            map.invalidateSize(false);
            var smAndDown = false;
            if (window === window.top)
                smAndDown = this.$vuetify.breakpoint.smAndDown
            else
                smAndDown = parent.document.body.clientWidth < 960;
            if (smAndDown) {


                document.querySelector('#mapcontainer').style.height = "100%";
                if (this.estimator == false)
                    document.querySelector('#map').style.height = "100%";
                else
                    document.querySelector('#map').style.height = "100%";
                if (map != null)
                    map.invalidateSize(true);
                this.marginTop = "56px";
            } else {


                document.querySelector('#mapcontainer').style.height = "100%";
                if (this.estimator == false)
                    document.querySelector('#map').style.height = "100%";
                else
                    document.querySelector('#map').style.height = "100%";

                if (map != null)
                    map.invalidateSize(true);
                this.marginTop = "0px";
            }


        }, 200),
    }
    ,

    computed: {
        pagePermissions() {
            if (typeof window.openedFromPto !== 'undefined' && window.openedFromPto === true) {
                // If openedFromPto is explicitly true, skip permission checks
                return [];
            }
            let pageId = 4
            return window.userpermissions[pageId] || [];
          },
          pagePermissionEdit() {
              if (typeof window.openedFromPto !== 'undefined' && window.openedFromPto === true) {
                  return true;  // Allow all permissions when openedFromPto is explicitly true
              }
            const permission = this.pagePermissions.find(a => a["Permission"] == 'Edit');
            return permission ? permission['Value'] : true;
          },
          pagePermissionDelete() {
              if (typeof window.openedFromPto !== 'undefined' && window.openedFromPto === true) {
                  return true;  // Allow all permissions when openedFromPto is explicitly true
              }
            const permission = this.pagePermissions.find(a => a["Permission"] == 'Delete');
            return permission ? permission['Value'] : true;
          },
          pagePermissionAdd() {
              if (typeof window.openedFromPto !== 'undefined' && window.openedFromPto === true) {
                  return true;  // Allow all permissions when openedFromPto is explicitly true
              }
            const permission = this.pagePermissions.find(a => a["Permission"] == 'Add');
            return permission ? permission['Value'] : true;
          },
        takeoffUser() {
            return store.get('map/takeoffLink');
        },
        saveText() {
            if (this.$store.get('estimator/currentEstimateId')) {
                return 'Save and Return';
            }
            return 'Save';
        },
        uniqueSites() {
            if (!this.files) return [];
            return _.uniqBy(this.files, 'sitename')
                .sort((a, b) => {
                    const siteA = a.sitename || '';
                    const siteB = b.sitename || '';
                    return siteA.localeCompare(siteB);
                });
        },
        uniqueCreators() {
            if (!this.files) return [];
            return _.uniqBy(this.files, 'CreatorName')
                .sort((a, b) => {
                    const creatorA = a.CreatorName || '';
                    const creatorB = b.CreatorName || '';
                    return creatorA.localeCompare(creatorB);
                });
        },
        uniqueUpdaters() {
            if (!this.files) return [];
            return _.uniqBy(this.files, 'UpdaterName')
                .sort((a, b) => {
                    const updaterA = a.UpdaterName || '';
                    const updaterB = b.UpdaterName || '';
                    return updaterA.localeCompare(updaterB);
                });
        },
        filteredFiles() {
            if (!this.files) return [];
            let k = this.files;

            if (this.filter?.sites?.length > 0) {
                k = k.filter(item => {
                    const sitename = item.sitename || '';
                    return this.filter.sites.includes(sitename);
                });
            }

            if (this.filter?.creator?.length > 0) {
                k = k.filter(item => {
                    const creatorName = item.CreatorName || '';
                    return this.filter.creator.includes(creatorName);
                });
            }

            if (this.filter?.updater?.length > 0) {
                k = k.filter(item => {
                    const updaterName = item.UpdaterName || '';
                    return this.filter.updater.includes(updaterName);
                });
            }

            return k;
        },
        animStatus: {
            get() {
                this.forceRecompute++;
                if (typeof this.selectedItem.feature.properties.anim != 'undefined')
                    if (this.selectedItem.feature.properties.anim == "true")
                        return true;
                    else
                        return false;
                else
                    return false;
            },
            set(val) {
                if (val == true) {
                    if (typeof this.selectedItem.feature.properties.AnimIcon == 'undefined') {
                        this.snackbar.text = 'Please select an icon first.'
                        this.snackbar.snackbar = true;
                        this.selectedItem.feature.properties.anim = "false";
                        this.forceRecompute++;
                    } else if (this.selectedItem.feature.properties.AnimIcon == "") {
                        this.snackbar.text = 'Please select an icon first.'
                        this.snackbar.snackbar = true;
                        this.selectedItem.feature.properties.anim = "false";
                        this.forceRecompute++;
                    } else {
                        this.selectedItem.feature.properties.anim = "true";
                        globalMapManager.addPolylineAnimation(this.selectedItem, true);
                    }
                } else {
                    this.selectedItem.feature.properties.anim = "false";
                    globalMapManager.addPolylineAnimation(this.selectedItem, false);
                }
            }
        },
        groups: VuexPathify.get('groups/groups'),
        ...VuexPathify.sync('mapview', [
            'disableeventlistner'
        ]),
        markers() {
            return myicons.Markers;
        },
        animMarkers() {
            return myicons.Animation.type[0].icons;
        },
        photoMarkers() {
            return myicons.Photo.type[0].icons;
        },
        groupItems() {
            var items = [];
            var l = store.get('groups/groups');
            for (var category in l) {
                var item = {
                    header: category
                };
                items.push(item);
                var value = l[category];
                for (var j = 0; j < value.length; j++) {
                    var item = {}
                    item.value = value[j].value
                    item.text = value[j].name
                    items.push(item);
                }
            }
            return items;
        },
        mini: {
            get() {
                return this.$store.getters.getMini;
            },
            set(value) {
                this.$store.commit('setMini', value)
            }
        },
        cLogo() {
            return mycompanylogo;
        },
        cName() {
            return mycompanyname;
        },
        initials() {
            return myfname.charAt(0) + mylname.charAt(0);
        },
        loaded: VuexPathify.get('mapview/loaded'),
        loadedtype: VuexPathify.get('mapview/loadedtype'),
        loadedvar: VuexPathify.get('mapview/loadedvar'),
        shapeCounter: VuexPathify.sync('map/shapeCounter'),
        clone: VuexPathify.sync('map/clone'),
        mapState: VuexPathify.sync('map/mapState'),
        fileName: VuexPathify.sync('map/fileName'),
        fileID: VuexPathify.get('map/fileID'),
        fileID: VuexPathify.get('map/fileID'),
        globalSelectedGroup: VuexPathify.sync('groups/globalSelectedGroup'),
        mergemapDialog: VuexPathify.sync('map/mergemapDialog'),
        shareMapDialog: VuexPathify.sync('map/shareMapDialog')
    }
}
