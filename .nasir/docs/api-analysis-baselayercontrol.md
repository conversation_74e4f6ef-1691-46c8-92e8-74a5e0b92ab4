# API Analysis: Base Layer Control Component

## Overview
Analysis of active API calls in `/backoffice/mapbuilder/js/components/baselayercontrol.js` for integration between frontend and backend systems.

## Active API Calls to tiles.sitefotos.com

### 1. Search Cached Imagery
**Endpoint:** `https://tiles.sitefotos.com/api3/tiles/searchcached`
**Method:** GET
**Function:** `getCachedImagery()`
**Trigger:** When switching to high-resolution imagery layer

**Query Parameters:**
- `accesscode`: User access code from store (`store.get('map/accessCode')`)
- `bounds`: Map bounds in bbox format (`boundsDG.toBBoxString()`)
- `worker`: Worker flag (1 if takeoff link, 0 otherwise)

**Request Headers:** None specified (default fetch headers)

**Expected Response:**
- Array of cached imagery objects
- Each object contains: `id`, `date`, `tiles_provider`, `tiles_accesscode`, `bounds`, `coverage`
- Response processed to add `displayDate` and `displayCoverage` fields

**Error Handling:** Basic JSON parsing, no specific error handling

---

### 2. Search Available Imagery
**Endpoint:** `https://tiles.sitefotos.com/api3/tiles/search`
**Method:** GET
**Function:** `searchHighRes()`
**Trigger:** When searching for new high-resolution imagery

**Query Parameters:**
- `accesscode`: User access code
- `bounds`: Map bounds in bbox format
- `worker`: Worker flag (1 if takeoff link, 0 otherwise)

**Expected Response:**
```json
{
  "nearsearch": {
    "im": [
      {
        "date": "YYYY-MM-DD",
        "tile": "tile_id",
        "estimate": number
      }
    ],
    "estimate": number
  },
  "eaglesearch": [
    {
      "id": "layer_id",
      "date": "YYYY-MM-DD",
      "resolution": number,
      "isContained": boolean,
      "cost": number,
      "bounds": array,
      "minZoom": number,
      "maxZoom": number,
      "tileMatrixSet": string
    }
  ]
}
```

**Data Processing:**
- Nearmap results: adds `displayDate`, `formattedDate`, `estimate`, `cost`, `tileid`, `provider`
- EagleView results: adds `displayDate` and other metadata
- Results sorted by date (newest first)

---

### 3. Search Cached by URL
**Endpoint:** `https://tiles.sitefotos.com/api3/tiles/searchcachedbyurlmapbuilder`
**Method:** GET
**Function:** `getCachedImagery()` (conditional)
**Trigger:** When `fileOpenTrigger` is true and `tileUrl` exists

**Query Parameters:**
- `accesscode`: User access code
- `url`: Existing tile URL (`this.tileUrl`)
- `bounds`: Map bounds in bbox format

**Expected Response:** Similar to searchcached endpoint

---

### 4. Get Cached Tile
**Endpoint:** `https://tiles.sitefotos.com/api3/tiles/getcachedtile`
**Method:** GET
**Function:** `downloadCached(tileid)`
**Trigger:** When selecting cached imagery for display

**Query Parameters:**
- `accesscode`: User access code (or takeoff client access code)
- `worker`: Worker flag
- `tileid`: Tile ID to download
- `bid`: Building ID (`store.get('map/buildingID')`)
- `mid`: File ID (`store.get('map/fileID')`)

**Expected Response:**
```json
{
  "error": boolean,
  "url": "image_url",
  "bounds": "geojson_bounds",
  "date": "YYYY-MM-DDTHH:mm:ss",
  "provider": "provider_name",
  "quota": number
}
```

---

### 5. Get User Quota
**Endpoint:** `https://tiles.sitefotos.com/api3/general/getquota`
**Method:** GET
**Function:** `getQuota()`
**Trigger:** On component mount and after purchases

**Query Parameters:**
- `accesscode`: User access code

**Expected Response:**
```json
{
  "allocation": number,
  "usage": number
}
```

**Data Processing:** Calculates remaining quota as `allocation - usage`

---

### 6. Download Nearmap Tile
**Endpoint:** `https://tiles.sitefotos.com/api3/tiles/downloadnearmaptile`
**Method:** GET
**Function:** `downloadNearmap(dataset)`
**Trigger:** When purchasing Nearmap imagery

**Query Parameters:**
- `accesscode`: User access code (or takeoff client access code)
- `bounds`: Padded map bounds
- `date`: Imagery date
- `cost`: Purchase cost
- `worker`: Worker flag
- `bid`: Building ID
- `mid`: File ID

**Expected Response:**
```json
{
  "url": "image_url",
  "bounds": "geojson_bounds",
  "date": "YYYY-MM-DDTHH:mm:ss",
  "provider": "nearmap",
  "quota": number,
  "cost": number
}
```

**Error Handling:**
- HTTP 402: Insufficient quota error with detailed message
- Generic error handling for other status codes

---

### 7. EagleView Purchase
**Endpoint:** `https://tiles.sitefotos.com/api3/tiles/eagleview/purchase`
**Method:** POST
**Function:** `purchaseEagleViewLayer()`
**Trigger:** When purchasing EagleView imagery

**Request Headers:**
- `Content-Type: application/json`

**Request Body:**
```json
{
  "accesscode": "user_access_code",
  "layer": "layer_id",
  "bounds": [[west, south], [east, south], [east, north], [west, north], [west, south]],
  "tileMatrixSet": "tile_matrix_set",
  "imageryDate": "YYYY-MM-DD",
  "minZoom": number,
  "maxZoom": number,
  "worker": 0|1
}
```

**Expected Response:**
```json
{
  "token": "jwt_token",
  "cost": number,
  "remainingQuota": number,
  "reason": "purchase_reason",
  "purchaseId": number
}
```

**Error Handling:**
- HTTP 402: Insufficient quota with detailed error message
- Stores JWT token in `eagleViewTokens[layer]`

---

### 8. EagleView Token Generation
**Endpoint:** `https://tiles.sitefotos.com/api3/tiles/eagleview/token`
**Method:** POST
**Function:** `generateEagleViewToken()`
**Trigger:** When accessing existing EagleView purchases

**Request Headers:**
- `Content-Type: application/json`

**Request Body (ID-based):**
```json
{
  "accesscode": "user_access_code",
  "purchaseId": number,
  "tileMatrixSet": "GoogleMapsCompatible_7-22"
}
```

**Request Body (Legacy layer-based):**
```json
{
  "accesscode": "user_access_code",
  "layer": "layer_id",
  "tileMatrixSet": "GoogleMapsCompatible_7-22"
}
```

**Expected Response:**
```json
{
  "token": "jwt_token",
  "bounds": [[lng, lat], [lng, lat], ...]
}
```

---

## Active API Calls to /crux Endpoint

### 1. EagleView Tile Requests
**Endpoint Pattern:** `/crux/eagleview/tile/{layer_id}/{z}/{x}/{y}.jpeg`
**Method:** GET (via Leaflet tile layer)
**Function:** Tile layer creation in multiple methods
**Trigger:** When displaying EagleView imagery

**Query Parameters:**
- `token`: JWT token obtained from purchase or token generation

**Usage Locations:**
1. `selectEagleView()` - Line 572
2. `displayEagleViewCached()` - Line 705  
3. `changeHighRes()` (file opening) - Line 834

**Authentication:** JWT token in query parameter
**Expected Response:** JPEG image tiles
**Error Handling:** Handled by Leaflet tile layer system

---

## Authentication & Authorization

**Primary Authentication:**
- `accesscode` parameter in all tiles.sitefotos.com requests
- Retrieved from `store.get('map/accessCode')`
- Alternative: `store.get('map/takeoffClientAccessCode')` for takeoff links

**EagleView Authentication:**
- JWT tokens obtained via purchase or token generation endpoints
- Tokens stored in `this.eagleViewTokens[layer_id]`
- Used in /crux tile requests

**Worker Context:**
- `worker` parameter: 1 for takeoff links, 0 for regular users
- Affects access code selection and pricing

---

## Data Transformations

**Bounds Processing:**
- Input: Leaflet LatLngBounds object
- Transformation: `.toBBoxString()` for API calls
- Padding applied: `((area + 400) / area) - 1`

**Date Formatting:**
- API format: `YYYY-MM-DDTHH:mm:ss` or `YYYY-MM-DD`
- Display format: `MM/DD/YY` using moment.js
- Handles null dates for EagleView records

**Quota Management:**
- Multiple response formats supported
- Real-time quota updates after purchases
- Quota enforcement with HTTP 402 responses

---

## Error Handling Patterns

**HTTP Status Codes:**
- 402: Insufficient quota (enhanced in /api3)
- Other 4xx/5xx: Generic error handling

**Error Messages:**
- Quota errors: Show remaining vs required amounts
- Generic errors: "Please try again" messages
- User notifications via snackbar component

**Fallback Behavior:**
- Failed purchases: Revert to previous layer
- Missing data: Default to "N/A" values
- Network errors: Show user-friendly messages
