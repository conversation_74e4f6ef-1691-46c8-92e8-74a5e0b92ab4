# Refactoring Strategy for Map Builder (Vue 2 Revision)

This document outlines a phased refactoring strategy to modernize the `backoffice/mapbuilder` module. The goal is to move from a global singleton pattern to a more robust, maintainable, and performant architecture using Vue.js's native reactivity and dependency injection systems.

## Phase 1: Service Encapsulation

### Objective
The first phase is to decouple the core map logic from the Vue component lifecycle by encapsulating it within a plain JavaScript class, `MapService`. This service will manage map state and interactions, but will no longer be a singleton attached to the `window` object. This improves testability, portability, and clarity of the map-related logic.

### Plan
1.  **Create `MapService.js`**: A new file, [`backoffice/mapbuilder/js/services/MapService.js`](backoffice/mapbuilder/js/services/MapService.js), will be created. This class will not be a Vue component.
2.  **Migrate Logic**: Core map management logic from [`mapmanager.js`](backoffice/mapbuilder/js/components/mapmanager.js:8) will be moved into `MapService`. This includes:
    *   Methods for creating, saving, and loading maps.
    *   Layer and preset management (`addPreset`, `deletePreset`, `addLayerPreset`).
    *   State properties like `presets`, `groups`, and the Leaflet map instance.
3.  **Remove Global Singleton**: The `window.globalMapManager = this;` line in [`mapmanager.js`](backoffice/mapbuilder/js/components/mapmanager.js:319) will be removed. The `MapService` will be instantiated and managed within the Vue application context.

### Code Snippet: `MapService.js` Structure

```javascript
// backoffice/mapbuilder/js/services/MapService.js

export class MapService {
  constructor(initialPresets) {
    this.map = null; // Leaflet map instance
    this.presets = initialPresets || {};
    this.groups = [];
    // ... other state properties
  }

  initializeMap(containerId) {
    // Logic to initialize the Leaflet map
  }

  addPreset(name, options) {
    // Logic to add a new preset
  }

  deletePreset(presetName) {
    // Logic to delete a preset
  }
  
  // ... other map manipulation methods
}
```

### Performance Justification
Encapsulating logic in a class does not directly impact runtime performance, but it significantly improves "developer performance" and maintainability. By creating a clear boundary between map logic and UI components, we reduce the cognitive load required to understand and modify the code. This leads to faster development, fewer bugs, and a more stable application, which are prerequisites for any further performance optimizations.

## Phase 2: Reactive State Management

### Objective
To introduce a dedicated, reactive state object using Vue's native capabilities. This object will hold only the data that components need to react to, such as the current zoom level, selected layer, or map status. Heavy, non-reactive data, like GeoJSON features and Leaflet layer instances, will be managed by the `MapService`, preventing unnecessary overhead from Vue's reactivity system.

### Plan
1.  **Create a Reactive State Object**: In the root map component ([`map.js`](backoffice/mapbuilder/js/pages/map.js)), we will use `Vue.observable()` to create a reactive state object.
2.  **Define Minimal State**: The state object will contain only the minimal data required for reactive UI updates. For example:
    *   `currentZoom`
    *   `selectedLayerId`
    *   `mapStatus` ('loading', 'editing', 'viewing')
3.  **Separate Concerns**: The `MapService` will remain responsible for managing the heavy map data. The reactive state object will only reflect the *state* of that data, not the data itself.

### Code Snippet: Reactive State

```javascript
// backoffice/mapbuilder/js/pages/map.js (Using Vue 2 Options API)
import Vue from 'vue';
import { MapService } from '../services/MapService.js';

export default {
  data() {
    return {
      // The reactive state is initialized here.
      // Vue will automatically make this object reactive.
      reactiveState: Vue.observable({
        currentZoom: 0,
        selectedLayerId: null,
        mapStatus: 'loading',
      }),
    };
  },
  provide() {
    // Provide the service and the reactive state to child components
    return {
      mapService: this.mapService,
      reactiveState: this.reactiveState,
    };
  },
  created() {
    // The service itself is not reactive and can be a simple property.
    this.mapService = new MapService();
  }
}
```

### Performance Justification
Vue's reactivity system is powerful, but it comes with an overhead. Every property on a reactive object is tracked for changes. For large, complex data structures like GeoJSON features or Leaflet layer objects, this tracking can be computationally expensive and memory-intensive.

By separating the state:
-   **Reactive State**: Small, simple object. Vue can track changes efficiently with minimal overhead.
-   **`MapService` Data**: Large, complex data is stored in plain JavaScript objects within the `MapService`. Vue does not track this data, avoiding performance bottlenecks. When this data changes, the `MapService` can emit events or update the reactive state object to trigger UI updates, but only when necessary.

This approach significantly improves performance by reducing the amount of data that Vue needs to monitor, leading to a more responsive UI, especially when dealing with large maps.

## Phase 3: Integration and Migration Strategy

### Objective
To provide the new `MapService` and reactive state to components using Vue's dependency injection system (`provide`/`inject`), and to outline a safe, incremental migration path for components to adopt the new architecture. This phase will implement the Strangler Fig Pattern, allowing the old and new systems to coexist during the transition.

### Plan
1.  **Provide Dependencies**: The `MapService` instance and the reactive state object will be `provided` by the root map component ([`map.js`](backoffice/mapbuilder/js/pages/map.js)). Child components will use `inject` to access them.
2.  **Incremental Migration**: Components will be migrated one by one. For each component, we will:
    *   Replace direct calls to `window.globalMapManager` with calls to the injected `mapService`.
    *   Replace direct data access with computed properties or watchers that react to the injected `reactiveState`.
3.  **Coexistence (Strangler Fig Pattern)**: During the transition, `window.globalMapManager` will continue to exist. A proxy or adapter can be created to synchronize state between the old and new systems if necessary, ensuring that components that have not yet been migrated continue to function correctly. This is a critical risk-mitigation step.

### Code Snippet: Before and After Migration

#### Before: `baselayercontrol.js`

```javascript
// backoffice/mapbuilder/js/components/baselayercontrol.js
// ...
export default {
  created() {
    window.globalBaseLayerControl = this;
  },
  methods: {
    someAction() {
      // Direct access to the global singleton
      if (window.globalMapManager) {
        window.globalMapManager.doSomething();
      }
    }
  }
}
```

#### After: `baselayercontrol.js` (with Vue 2 Options API)

```javascript
// backoffice/mapbuilder/js/components/baselayercontrol.js
// ...
export default {
  inject: ['mapService', 'reactiveState'],
  methods: {
    someAction() {
      // Access injected services via `this`
      this.mapService.doSomething();
      
      // Mutate the reactive state property directly
      this.reactiveState.mapStatus = 'updated';
    }
  }
}
```

### Performance Justification
Dependency injection itself does not have a direct runtime performance impact, but it enables a more organized and decoupled architecture, which is a prerequisite for performance improvements.

The incremental migration strategy (Strangler Fig Pattern) is crucial for performance and stability. It allows us to:
-   **Isolate Changes**: By migrating one component at a time, we can isolate and address any performance regressions or bugs that arise.
-   **Avoid "Big Bang" Risks**: A complete rewrite is risky and can introduce many bugs at once. An incremental approach allows for continuous delivery and testing.
-   **Maintain Application Stability**: The application remains functional throughout the migration process, as both old and new components can coexist. This is essential for a production environment.