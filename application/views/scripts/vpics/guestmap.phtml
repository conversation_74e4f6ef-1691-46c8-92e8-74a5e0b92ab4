<meta name="viewport" content="width=device-width, initial-scale=1.0,  maximum-scale=1.0, user-scalable=no">
<!--meta http-equiv="refresh" content="600" /-->
<div id="scriptcontainer1"></div>
<script src="/js/mapmaker/jquery-3.2.1.min.js"></script>
<script src="/js/mapmaker/jquery-ui.min.js"></script>
<script type="text/javascript" src="/js/mapmaker/moment.min.js"></script>
<script src="/js/mapmaker/leaflet.js"></script>
<script src="/js/mapmaker/leaflet-extend.js"></script>
<link rel="stylesheet" href="/css/mapmaker/leaflet.css" />
<script src="/js/mapmaker/Leaflet.draw.js"></script>
<script src="/js/mapmaker/Leaflet.Draw.Event.js"></script>
<link rel="stylesheet" href="/css/mapmaker/leaflet.draw.css" />
<script src="/js/mapmaker/Toolbar.js"></script>
<script src="/js/mapmaker/turf.min.js"></script>
<script src="/js/mapmaker/Tooltip.js"></script>
<script src="/js/mapmaker/GeometryUtil.js"></script>
<script src="/js/mapmaker/LatLngUtil.js"></script>
<script src="/js/mapmaker/LineUtil.Intersect.js"></script>
<script src="/js/mapmaker/Polygon.Intersect.js"></script>
<script src="/js/mapmaker/Polyline.Intersect.js"></script>
<script src="/js/mapmaker/TouchEvents.js"></script>
<script src="/js/mapmaker/DrawToolbar.js"></script>
<script src="/js/mapmaker/Draw.Feature.js"></script>
<script src="/js/mapmaker/Draw.SimpleShape.js"></script>
<script src="/js/mapmaker/Draw.Polyline.js"></script>
<script src="/js/mapmaker/Draw.Circle.js"></script>
<script src="/js/mapmaker/Draw.Marker.js"></script>
<script src="/js/mapmaker/Draw.Polygon.js"></script>
<script src="/js/mapmaker/Draw.Rectangle.js"></script>
<script src="/js/mapmaker/Control.Draw.js"></script>
<script src="/js/mapmaker/editable.js"></script>
<script src="/js/mapmaker/path.drag.js"></script>
<script src="https://cdn.jsdelivr.net/npm/lodash@4.17.5/lodash.min.js"></script>


<!--
<script src="/js/mapmaker/lasso.js"></script>

<script src="http://sitefotos.test/myjs/L.Path.Transform.js"></script>

-->


<script src="/js/mapmaker/handlebars.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/json3/3.3.2/json3.min.js"></script>
<script src="https://cdn.polyfill.io/v2/polyfill.min.js"></script>
<link rel="stylesheet" href="/css/mapmaker/introjs.css" />

<link rel="stylesheet" href="/css/leaflet/leaflet-text-icon.css" />
<script src="/js/leaflet/leaflet-text-icon.js"></script>
<script src="/js/curbdamage/numeral.min.js"></script>
<!--Load Leaflet polyline decorator to do polyine/marker animations -->
<script src="/js/leaflet/leaflet.polylineDecorator.js"></script>
<!--Load Google library for search with autocomplete -->
<script src="/js/leaflet-gplaces-autocomplete.js"></script>
<link rel="stylesheet" href="/css/leaflet-gplaces-autocomplete.css" />
<script>
function dummy() {}
window.dummy=dummy;
L.TileLayer.MaskedOffscreen = L.TileLayer.extend({
  initialize(urlTemplate, options = {}) {
    this._urlTemplate  = urlTemplate;
    this._tileBounds   = options.tileBounds;             // L.LatLngBounds
    this._layerMinZoom = options.minZoom   ?? -Infinity;
    this._layerMaxZoom = options.maxZoom   ??  Infinity;
    L.TileLayer.prototype.initialize.call(this, urlTemplate, options);
  },

  onAdd(map) {
    this._origMin = map.getMinZoom();
    this._origMax = map.getMaxZoom();
    map.setMinZoom(this._layerMinZoom);
    map.setMaxZoom(this._layerMaxZoom);
    return L.TileLayer.prototype.onAdd.call(this, map);
  },

  onRemove(map) {
    L.TileLayer.prototype.onRemove.call(this, map);
    map.setMinZoom(this._origMin);
    map.setMaxZoom(this._origMax);
  },

  createTile(coords, done) {
    /* ---------- 1. Create the final <img> that Leaflet will keep ---------- */
    const img = document.createElement('img');
    img.className = 'leaflet-tile';          // pick up Leaflet’s CSS
    if (this.options.crossOrigin) img.crossOrigin = '';

    /* ---------- 2. Early‑out if tile is outside AOI ---------- */
    const size   = this.getTileSize();
    const nwPt   = coords.scaleBy(size);
    const sePt   = nwPt.add(size);
    const nwLL   = this._map.unproject(nwPt, coords.z);
    const seLL   = this._map.unproject(sePt, coords.z);
    const tileLLB = L.latLngBounds(seLL, nwLL);

    if (!tileLLB.intersects(this._tileBounds)) {
      // Transparent 1×1 PNG data‑URI keeps things super small
      img.src =
        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAwMCAOXZPNsAAAAASUVORK5CYII=';
      done(null, img);
      return img;
    }

    /* ---------- 3. Fetch the raw tile image ---------- */
    const raw = new Image();
    if (this.options.crossOrigin) raw.crossOrigin = '';
    raw.onload = async () => {
      /* ----- 4. Draw + mask on an off‑screen canvas ----- */
      // Prefer real OffscreenCanvas (runs in worker if you move this code),
      // else fall back to hidden <canvas>.
      const off =
        typeof OffscreenCanvas !== 'undefined'
          ? new OffscreenCanvas(size.x, size.y)
          : Object.assign(document.createElement('canvas'), {
              width: size.x,
              height: size.y
            });

      const ctx = off.getContext('2d');
      ctx.drawImage(raw, 0, 0, size.x, size.y);

      // Clip to AOI polygon
      const corners = [
        this._tileBounds.getNorthWest(),
        this._tileBounds.getNorthEast(),
        this._tileBounds.getSouthEast(),
        this._tileBounds.getSouthWest()
      ];
      ctx.save();
      ctx.beginPath();
      corners.forEach((ll, i) => {
        const pw = this._map.project(ll, coords.z);
        const origin = coords.scaleBy(size);
        const p = pw.subtract(origin);
        i === 0 ? ctx.moveTo(p.x, p.y) : ctx.lineTo(p.x, p.y);
      });
      ctx.closePath();
      ctx.globalCompositeOperation = 'destination-in';
      ctx.fill();
      ctx.restore();

      /* ----- 5. Snapshot canvas → blob / data‑URL → img.src ----- */
      const assignSrc = (url) => {
        img.src = url;
        // call done when *this* img finishes loading the masked pixels
        img.onload = () => done(null, img);
        img.onerror = (e) => done(e, img);
      };

      if (off.convertToBlob) {
        // OffscreenCanvas path
        const blob = await off.convertToBlob();
        assignSrc(URL.createObjectURL(blob));
      } else {
        // Fallback <canvas>
        assignSrc(off.toDataURL('image/png'));
      }
    };

    raw.onerror = (e) => done(e, img);
    raw.src = this.getTileUrl(coords);
    return img; // Leaflet inserts this immediately; pixels arrive once ready
  }
});

/* factory helper */
L.tileLayer.maskedOffscreen = function (urlTempl, opts) {
  return new L.TileLayer.MaskedOffscreen(urlTempl, opts);
};

</script>
<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCHffyE3zbfCDeezpIT1pt8-mdgwYCmYvY&libraries=places&callback=dummy"></script>
<!--<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAjjDVZd76qolhZKtjRyDlHs1Vxnb6UAVM&libraries=places"></script>-->

<script src="/js/bundle.js"></script>

<script src="/js/Leaflet.Control.Custom.js"></script>
<link rel="stylesheet" type="text/css" href="/css/spectrum.css">
<script type="text/javascript" src="/js/spectrum.js"></script>
<script type="text/javascript" src="/js/geo.js"></script>
<script type="text/javascript" src="/js/pako_inflate.js"></script>

<script type="text/javascript" src="/js/jszip.min.js"></script>
<script type="text/javascript" src="/js/shapeutils.js"></script>

<link href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet" crossorigin="anonymous">

<link rel="stylesheet" href="/fonts/polygon/style.css">
<!--link href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0-beta/css/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous">

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/mdbootstrap/4.3.2/css/mdb.min.css"-->
<link rel="stylesheet" href="https://storage.googleapis.com/code.getmdl.io/1.0.2/material.indigo-pink.min.css" />
<script src="https://storage.googleapis.com/code.getmdl.io/1.0.2/material.min.js"></script>
<link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" />
<link rel="stylesheet" href="/css/mapmaker/bootstrap-colorselector.min.css">
<script src="/js/mapmaker/bootstrap-colorselector.min.js"></script>
<script src="/js/mapmaker/datalist-polyfill.js"></script>
<style type="text/css">
    @page {
        size: A1 landscape;
    }

    .sp-palette {
        max-width: 200px;
    }

    .label {
        background: #444;
        padding: 8px;
        border-radius: 4px;
        color: #eee;
        margin-top: 10px;
        display: inline-block;
    }
</style>
<style>
    div.scrollmenu {
        background-color: #ccc;
        overflow: auto;
        white-space: nowrap;
        text-align: left;
    }

    div.scrollmenu a {
        display: inline-block;
        color: white;
        text-align: center;
        padding: 14px;
        text-decoration: none;
    }

    div.scrollmenu a:hover {
        background-color: #777;
    }
</style>
<style>
    body {
        padding: 0;
        margin: 0;
        position: relative;
    }

    html,
    body,
        {
        height: 100vh;
        width: 100vw;
    }

    /*******************************************************/

    form label {
        width: auto;
        padding-top: 0;
        clear: both;
    }

    .nobg {
        background: rgba(255, 255, 255, 0);
    }

    .panel-body {
        padding: 0px !important;
    }

    .panel-group .panel+.panel {
        margin-top: 0px;
    }

    .myButton {
        -moz-box-shadow: inset 0px 1px 3px 0px #9fb4f2;
        -webkit-box-shadow: inset 0px 1px 3px 0px #9fb4f2;
        box-shadow: inset 0px 1px 3px 0px #9fb4f2;
        background: -webkit-gradient(linear, left top, left bottom, color-stop(0.05, #7892c2), color-stop(1, #476e9e));
        background: -moz-linear-gradient(top, #7892c2 5%, #476e9e 100%);
        background: -webkit-linear-gradient(top, #7892c2 5%, #476e9e 100%);
        background: -o-linear-gradient(top, #7892c2 5%, #476e9e 100%);
        background: -ms-linear-gradient(top, #7892c2 5%, #476e9e 100%);
        background: linear-gradient(to bottom, #7892c2 5%, #476e9e 100%);
        filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#7892c2', endColorstr='#476e9e', GradientType=0);
        background-color: #7892c2;
        -moz-border-radius: 5px;
        -webkit-border-radius: 5px;
        border-radius: 5px;
        border: 1px solid #4e6096;
        display: inline-block;
        cursor: pointer;
        color: #ffffff;
        font-family: Arial;
        font-size: 15px;
        font-weight: bold;
        padding: 5px 10px;
        text-decoration: none;
        text-shadow: 0px -1px 0px #283966;
    }

    .myButton:hover {
        background: -webkit-gradient(linear, left top, left bottom, color-stop(0.05, #476e9e), color-stop(1, #7892c2));
        background: -moz-linear-gradient(top, #476e9e 5%, #7892c2 100%);
        background: -webkit-linear-gradient(top, #476e9e 5%, #7892c2 100%);
        background: -o-linear-gradient(top, #476e9e 5%, #7892c2 100%);
        background: -ms-linear-gradient(top, #476e9e 5%, #7892c2 100%);
        background: linear-gradient(to bottom, #476e9e 5%, #7892c2 100%);
        filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#476e9e', endColorstr='#7892c2', GradientType=0);
        background-color: #476e9e;
    }

    .myButton:active {
        position: relative;
        top: 1px;
    }
</style>
<style>
    .leaflet-div-icon2 {
        background: transparent;
    }

    .leaflet-marker-icon .number {
        position: relative;
       // top: -37px;
       // font-size: 14px;
        top: -33px;
        font-size: 11px;
        text-align: center;
    }
</style>
<style>
    .leaflet-touch .leaflet-control-layers-toggle {
        width: 32px !important;
        height: 32px !important;
    }

    .leaflet-control-layers-toggle {
        width: 32px !important;
        height: 32px !important;
    }

   
    @media only screen and (min-device-width: 320px) and (max-device-width: 767px) {


        .cellPhoto {
            max-width: 190px;
        }

        .companylogoclass {
            width: 75px;
        }
    }

    @media only screen and (min-device-width: 768px) {
 
        .cellPhoto {
            max-width: 190px;
        }

        .companylogoclass {
            width: 150px;
        }
    }
    @media only print {
        .companylogoclass {
            width: 150px;
        }
    }
</style>
<style type="text/css" media="screen">
    .thumbnail {
        margin-bottom: 0px;
    }

    th {
        background-color: #ffffff;
    }

    tr:nth-child(even) {
        background-color: #ffffff;
    }

    .borderclass td {
        border: 1px solid #ccc;
    }

    .unselectable {
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        cursor: pointer;
    }

    [hidden] {
        display: none !important;
    }

    .single {
        padding: 0px 15px;
        background: #fcfcfc;
        border: 1px solid #f0f0f0;
    }

    .single h3.side-title {
        margin: 0;
        margin-bottom: 10px;
        padding: 0;
        font-size: 20px;
        color: #333;
        text-transform: uppercase;
    }

    .single h3.side-title:after {
        content: '';
        width: 60px;
        height: 1px;
        background: #ff173c;
        display: block;
        margin-top: 6px;
    }

    .single ul {
        margin-bottom: 0;
    }

    .single li a {
        color: #666;
        font-size: 14px;
        text-transform: uppercase;
        border-bottom: 1px solid #f0f0f0;
        line-height: 40px;
        display: block;
        text-decoration: none;
    }

    .single li a:hover {
        color: #ff173c;
    }

    .single li:last-child a {
        border-bottom: 0;
    }

    ul#layerlist li a:hover,
    ul#nav li.active a {
        color: #000000;
        text-decoration: none;
    }

    ul,
    li a {
        cursor: pointer;
    }

    .btn span.glyphicon {
        opacity: 0;
    }

    .btn.active span.glyphicon {
        opacity: 1;
    }

    .thumbnail img {
        min-height: 50px;
        height: 50px;
    }

    .thumbnail {
        display: block;
        padding: 4px;
        line-height: 1.42857143;
        background-color: transparent !important;
        border: none !important;
        border-radius: 0 !important;
        -webkit-transition: border .2s ease-in-out;
        -o-transition: border .2s ease-in-out;
        transition: border .2s ease-in-out;
    }
</style>
<style>
    .helpimage {
        width: 30px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        text-decoration: none;
        background-image: linear-gradient(transparent, transparent), url(https://b3ncr.github.io/dist/images/spritesheet.svg);
        background-repeat: no-repeat;
        background-size: 270px 30px;
        background-clip: padding-box;
        border: none;
    }

    .polyline {
        background-position: 0 -1px;
    }

    .polygon {
        background-position: -29px -1px;
    }

    .rectangle {
        background-position: -60px -1px;
    }

    .circle {
        background-position: -90px -1px;
    }

    .marker {
        background-position: -120px -1px;
    }

    .texttool {
        background-image: url(/images/text_icon.svg);
        background-size: 28px 28px;
        width: 30px;
        height: 30px;
    }

    .layertool {
        background-image: url(https://b3ncr.github.io/docs/examples/libs/images/layers.png);
        background-size: 28px 28px;
        width: 30px;
        height: 30px;
    }

    .searchtool {
        background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABOUlEQVQ4T6XTLUgmQRgH8N+Ligd+FOu1ww+wKPhxlwxXxHYnqCAGQZtgMBgU4eWaXLhyCgYxiAYVk6igJotgEaNiNYgGL6kHJwOzsO+yGzw3zszzm5nnP1vyzq+UU9+JUbTiCWdYw13eXmmgCr8wlbPwERPYys6lgVA8jSvM4RQfMIQF1KIfR2kkAdpxiRv04CGzUx9OcI02/EvmE+AH5jGG9YK+bmMQ3TjPApsYQXPcJc+Ywc/Y4I0ssIpxdOCi4ATl2Ivv2M0Ck1jBImZzgOrYoxZ8xG0WqI9Hb4pX2UkhNViKMe5jIC+FMPYVezGu4xhjHb7hUyx6wXDeFRK0C79jlMnYX4SmhZfZiwok7ymHwpBGyPs5RnaPRhzicxopAop+sYAc4Av+BPStQIAbsByffPl/gIrTvQLbJDoR8K3H6QAAAABJRU5ErkJggg==");
        background-size: 16px 16px;
        background-position: 6px 6px;
    }

    .nav>li>a {
        padding-right: 10px;
        padding-left: 10px;
    }

    .leaflet-touch-icon {
        width: 10px !important;
        height: 10px !important;
        margin-left: -5px !important;
        margin-top: -5px !important;
    }

    .csscircle {
        width: 22px;
        height: 22px;
        -moz-border-radius: 11px;
        -webkit-border-radius: 11px;
        border-radius: 11px;
        border-style: solid;
        border-width: 1px;
    }

    .csssquare {
        width: 22px;
        height: 22px;
        border-style: solid;
        border-width: 1px;
    }

    .csspolyline {
        width: 4px;
        height: 22px;
        -webkit-transform: rotate(45deg);
        -moz-transform: rotate(45deg);
        -ms-transform: rotate(45deg);
        -o-transform: rotate(45deg);
        transform: rotate(45deg);
        border-style: solid;
        border-width: 1px;
    }

    .nav-tabs>li.active>a,
    .nav-tabs>li.active>a:hover,
    .nav-tabs>li.active>a:focus {
        font-weight: bold;
        color: #fff;
        cursor: default;
        background-color: #337ab7;
        border: 1px solid #ddd;
    }

    .firstindent ul li {
        margin-left: 40px;
    }

    .layer-switch {
        position: absolute;
        bottom: 0px;
        left: 0px;
    }

    .name {
        padding: 0 10px;
        display: inline-block;
        vertical-align: top;
        background: #fff;
    }
</style>
<script>
    var myimages = '';;
    var imglist = [];
    var bldglist;
    var mybuildings;
    var mygroups;
    var mylayers;
    var companylogo;
    var accessCode = '<?=$this->ownerAccessCode?>';
    var mapID = '<?=$this->mapID?>';
    var buildingID = '<?=$this->buildingID?>';
    var isMobile = false; //initiate as false
    // device detection
    if(/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|ipad|iris|kindle|Android|Silk|lge |maemo|midp|mmp|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(navigator.userAgent) 
        || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(navigator.userAgent.substr(0,4))) { 
        isMobile = true;
    }
   
   

</script>
<svg>
    <defs>
        <filter id="outline_selected" x="-5000%" y="-5000%" width="10000%" height="10000%">
            <feFlood result="flood" flood-color="white" flood-opacity="1"></feFlood>
            <feComposite in="flood" result="mask" in2="SourceGraphic" operator="in"></feComposite>
            <feMorphology in="mask" result="dilated" operator="dilate" radius="1"></feMorphology>
            <feGaussianBlur in="dilated" result="blurred" stdDeviation="1"></feGaussianBlur>
            <feComposite in="blurred" in2="SourceGraphic" operator="arithmetic" k2="1" k3="-1" result="nocombine"></feComposite>
            <feMerge>
                <feMergeNode in="nocombine"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
</svg>
<?php
 if (isset($this->groupData)){
	$grouplist = json_encode($this->groupData,JSON_UNESCAPED_SLASHES);
	if(isset($this->devserver)) {
   $grouplist = $this->groupData;
}
echo "<script>mygroups = {$grouplist};</script>";

if (isset($this->accessCode)){
		echo "<script>var vid = '$this->accessCode';</script>";
	}

}


if (isset($this->layers)){
  $layerlist = json_encode($this->layers,JSON_UNESCAPED_SLASHES);
  if(isset($this->devserver)) {
  $layerlist = $this->layers; //comment this
  echo "<script>mylayers = JSON.parse('{$layerlist}');</script>";
  }
  else{
  // echo "<script>mylayers = JSON.parse({$layerlist});</script>"; //comment this
	echo "<script>mylayers = JSON.parse({$layerlist});</script>";
  }
}

if (isset($this->companylogo)){
  $companylogo = $this->companylogo;
    echo "<script>companylogo = '{$companylogo}';</script>";

}

?>
<style>
  
        hr {
            margin-top: 5px;
            margin-bottom: 10px;
            border: 0;
            border-top: 1px solid #eee;
        }

        @keyframes dash {
            to {
                stroke-dashoffset: 1000;
            }
        }

        #vertexpopup {
            display: none;
            z-index: 999999;
            background: white;
            position: absolute;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
<!--span style="position: fixed;top:0;right:0;z-index:2001;font-size:30px;cursor:pointer;color:white" onclick="openNav()">&#9776;</span-->
<script class="extract-script">

      function saveLocalStorage(name, object, type) {
            if (type == "JSON")
                localStorage.setItem(name, JSON.stringify(object));
            else
                localStorage.setItem(name, object);
        }

        function retrieveLocalStorage(name) {
            return localStorage.getItem(name);
        }

        var style = {
            'default': {
                'opacity': '1'
            },
            'highlight': {
                'opacity': '0.3'
            }
        };
        var highlight;

        function bindTooltip(layer) {
            return layer.properties.name + " : " + layer.properties.preset;
        }

    </script>


<div id="map" class="" style="min-width:300px;min-height:300px; width: 100%; height: -webkit-calc(100%);height: -moz-calc(100%); height:calc(100%); position:absolute; top:0px;"></div>

<style>
    .max200 {
            max-height: 200px;
            overflow-y: auto;
        }

        #Selected {
            position: absolute;
            z-index: 2001;
            margin: 10px;
            border: 1px solid rgb(0, 0, 0);
            border-radius: 10px;
            background-color: rgb(255, 255, 255) !important;
            font-family: Roboto, Arial, sans-serif;
            font-size: 12px;
            width: 270px !important;
            padding: 1px !important;
            display: none;
        }

        #Selected.no-pseudo-before:before {
            display: none;
        }

        #Selected.no-pseudo-after:after {
            display: none;
        }

        #Selected:before {
            content: '';
            position: absolute;
            top: 0%;
            left: 50%;
            margin-left: -10px;
            margin-top: -10px;
            width: 0;
            height: 0;
            border-bottom: solid 10px white;
            border-left: solid 10px transparent;
            border-right: solid 10px transparent;
        }

        .properties-table {
            width: 240px;
            border-collapse: collapse;
            margin: auto;
            border: 0px solid black;
            table-layout: fixed;
        }

        .properties-table td {
            text-align: left;
            padding-left: 5px;
        }

        .properties-table td input {
            width: 100%;
            box-sizing: border-box;
        }

        .properties-table td select {
            width: 100%;
            box-sizing: border-box;
        }

        .properties-table td textarea {
            width: 100%;
            box-sizing: border-box;
        }

        #Selected:after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -10px;
            width: 0;
            height: 0;
            border-top: solid 10px white;
            border-left: solid 10px transparent;
            border-right: solid 10px transparent;
        }

        .properties-table tr:nth-child(even) {
            background-color: #f7f7f7;
        }

        section.content {
            flex: 1;
        }

        .cellPhoto {
            height: auto;
            width: auto;
            /* max-width: 100px;
	max-height: 100px;*/
            display: block;
            margin: auto;
        }

        #cellIcon {
            height: auto;
            width: auto;
            max-width: 20px;
            /* max-height: 20px;*/
            display: inline-block;
            margin: auto;
        }

        .maincontainer .list-group-item {
            padding-top: 2px;
            padding-bottom: 2px;
            border: none;
        }

        .maincontainer ul {
            margin: 0 auto;
            text-align: center;
        }

        .maincontainer li {
            display: inline-block;
            vertical-align: top;
        }

        .maincontainer li img {
            height: auto;
            width: auto;
            max-width: 30px;
            max-height: 30px;
            display: block;
            margin: auto;
        }

        .maincontainer input {
            padding-top: 1px;
            width: 100% !important;
            padding-bottom: 1px;
            height: 20px;
            font-family: inherit;
            font-size: inherit;
            line-height: inherit;
        }

        .maincontainer select {
            width: 100% !important;
            padding-top: 1px;
            padding-bottom: 1px;
            height: 20px;
            font-family: inherit;
            font-size: inherit;
            line-height: inherit;
            margin-left: 5px;
        }

        .maincontainer textarea {
            max-height: 70px;
            padding-top: 1px;
            padding-bottom: 1px;
            font-family: inherit;
            font-size: inherit;
            line-height: inherit;
            resize: none;
        }

        .maincontainer .dropdown-icons {
            max-width: 20px;
            max-height: 20px;
        }

        .maincontainer .col-sm-4 {
            padding-right: 0px;
        }

        .maincontainer .col-sm-12 {
            padding-right: 0px;
        }

        .maincontainer .btn {
            font-size: 10px;
        }

        .sp-dd {
            display: none;
        }


        ::-webkit-scrollbar-thumb {
            background-clip: padding-box;
            background-color: rgba(0, 0, 0, .3);
            border: 5px solid transparent;
            border-radius: 10px;
            min-height: 20px;
            min-width: 20px;
            height: 5px;
            width: 5px;
        }

        ::-webkit-scrollbar {
            height: 15px;
            width: 15px;
            background: white;
        }

        ::-webkit-scrollbar-button {
            height: 0;
            width: 0;
        }


        .hide {
            display: none;
        }


        .panel {
            border: none;
        }
    </style>
<script>
    function displayiconselector(data) {
            $('#iconselectcontainer').css("top", map.getSize().y / 2);
            $('#iconselectcontainer').css("left", map.getSize().x / 2);
            $('#iconselectcontainer').css("display", "block");
        }
        $(document).click(function (event) {
            if (!$(event.target).closest('#iconselectcontainer').length) {
                if ($('#iconselectcontainer').is(":visible")) {
                    $('#iconselectcontainer').hide();
                }
            }
        });

        function closeproperties() {
            $('#iconselectcontainer').hide();
            $('#Selected').hide();
        }

        function showShare() {
            if (urlkey.length > 2) {
                /*  $.confirm({
                title: 'Share',
                content: '' +
				'<div class="form-group">' +
				'<h6>/vpics/guestmap?' + urlkey + '</h6>' +
				'</div>',

                buttons: {
				Email: {
				text: 'Email',
				btnClass: 'btn-blue',
				action: function () {

				}
				},
				Text: {
				text: 'Text',
				btnClass: 'btn-blue',
				action: function () {

				}
				},
				cancel: function () {

				},
				},

			});*/
                $("#savedfilename").val(currentFileName);
                $("#savedbid").val(bid);
                $('#sharediv').show();
            } else {
                $.snackbar({
                    content: "Please save this file to enable this functionality."
                });
            }
        }
    </script>
<div id="iconselectcontainer">
    <div id="iconselectpopup">
    </div>
</div>
<div id="Selected" class="container">
    <div class="properties-table" style="padding-top:10px;">
        <div id="closebutton">
            <span style="float:right;" onclick="closeproperties(); return false">
                <i class="fa fa-close fa-lg" aria-hidden="true"></i>
            </span>
        </div>
        <table id="propertiesTable" style="border: collapse; width:100%; margin-bottom:10px;">
            <tbody id="propertiestbodyid" style="border: none!important;" class="borderclass">
                <tr id="rowName" style="font-size: 16px; font-weight: bold;">
                    <td colspan="2" style="font-size: 18px; font-weight: bold;">
                        <input id="propertiesName" style="border-image-width:0!important;border:0!important;outline:0;box-shadow:none;border: none!important;cursor:default;-webkit-user-select: none; -moz-user-select: none;-ms-user-select: none; user-select: none;"
                            type="text">
                    </td>
                </tr>
                <tr id="rowPresets" style="display:none;">
                    <td style="width: 50%; min-width: 60px;">
                        <span>Group</span>
                    </td>
                    <td>
                        <select id="selectPresets">
                            <option value="none">None</option>
                        </select>
                    </td>
                </tr>
                <tr id="rowTextArea" style="">
                    <td colspan="2">
                        <div rows="2" id="cellTextArea" placeholder=" Desciption" style="max-height:100px;word-wrap:break-word;overflow-y:auto;width:233px"></div>
                    </td>
                </tr>
                <tr id="rowPhoto" style="display:none;">
                    <td style="padding:5px; width:100%;  text-align:center;" colspan="2">
                        <img id="cellPhoto" class="cellPhoto" src="/images/marker-red.png">
                    </td>
                </tr>
                <tr id="rowPhotoDate" style="display:none;">
                    <td>
                        <span>Date</span>
                    </td>
                    <td>
                        <span id="text_date"></span>
                    </td>
                </tr>
            </tbody>
        </table>
        <div style="display:inline-block; padding-bottom:10px; width:100%;">

            <span id="rowArea">
                <!--i class="fa fa-th" aria-hidden="true">&nbsp;&nbsp;</i-->
                <span id="text_area" style="font-size:13px; font-style:italic; font-weight:bold;"></span>&nbsp;&nbsp;&nbsp;</span>
            <span id="rowPerimeter">
                <!--i class="fa fa-arrows-v" aria-hidden="true">&nbsp;&nbsp;</i-->
                <span id="text_perim" style="font-size:13px; font-style:italic; font-weight:bold;"></span>
            </span>
        </div>

    </div>
</div>
</div>
</div>
<style>
    .activated {
            color: orange;
        }

        .deactivated {
            color: lightgray;
        }

        .activated:hover {
            cursor: pointer;
        }

        .deactivated:hover {
            cursor: pointer;
        }
    </style>


<div id="legend" style="display:none; z-index: 1000; position: absolute; bottom: 5px; right: 1px; font-family: Arial, sans-serif;
background-color: #fff!important; padding: 10px; margin: 10px; border: 1px solid #000; border-radius: 10px 10px 10px 10px; -moz-border-radius: 10px 10px 10px 10px; -webkit-border-radius: 10px 10px 10px 10px;">
</div>

<style>
    .floating-measurements {
            font-size: 12px;
            font-weight: 300;
        }

        .tooltip {
            z-index: 2050;
        }

        .pcontroller {
            max-height: 62px;
            overflow: hidden;
            font-size: 10px;
        }

        .toast {
            z-index: 2100;
        }

        .iconsize {
            width: 12px;
            /*height: 12px;*/
            float: left;
        }

    </style>
<div id="logo" style="z-index: 1000; position: absolute; bottom: 5px; left: 100px; ">
    <a href="" onclick="http://www.sitefotos.com/">
        <img src="/images/sitefotos_logo_4.svg" style="width: 100px; margin: 10px;"> </a>
</div>


<?php if (isset($this->companylogo)){ ?>
<div id="" style="z-index: 1000; position: absolute; top: 5px; right: 5px; ">
    <img id="companylogo" style="" class='companylogoclass'>
</div>
<?php } ?>
<script type='text/javascript'>
    if (typeof companylogo != 'undefined') {
        document.getElementById('companylogo').src = companylogo;
    }
</script>
<script type='text/javascript' class="extract-script">

    //If images are passed to the map from the gallery, imglist gets populated
    if (myimages.length > 0) {
        //regex to remove line returns
        someText = myimages.replace(/(\r\n|\n|\r)/gm, "");
        imglist = JSON.parse(someText);
    }
    var CustomDivIcon = L.Icon.extend({
  options: {
    ...L.Icon.prototype.options,
    iconSize: [12, 12],

    html: false,
    bgPos: null,
    className: 'leaflet-div-icon',

    span: false,
    spanClassName: '',
    textColor: 'white', // Added color property with default value 'white'
    background: false,
  },

  createIcon(oldIcon) {
    const div = (oldIcon && oldIcon.tagName === 'DIV') ? oldIcon : document.createElement('div'),
      options = this.options;

    let content = options.html;

    if (options.span) {
      const span = document.createElement('span');
      span.className = options.spanClassName;
      span.style.color = options.textColor; // Apply the color property to the text
      if(options.background) 
      {
        span.style.backgroundColor = '#FFFFFF';
        span.style.border = '1px solid #000000';
        span.style.padding = '4px';
        span.style.borderRadius = '4px';
        span.style.display = 'inline-block';
      } else {
        span.style.backgroundColor = 'transparent';
        span.style.border = 'none';
        span.style.padding = '4px';
        span.style.borderRadius = '0px';
        span.style.display = 'inline-block';
      }

      if (content instanceof Element) {
        span.appendChild(content);
      } else {
        span.innerHTML = content !== false ? content : '';
      }
      content = span;
    }

    if (content instanceof Element) {
      div.replaceChildren();
      div.appendChild(content);
    } else {
      div.innerHTML = content !== false ? content : '';
    }

    if (options.bgPos) {
      const bgPos = point(options.bgPos);
      div.style.backgroundPosition = `${-bgPos.x}px ${-bgPos.y}px`;
    }
    div.style.color = options.textColor; // Apply the color property to the text
    this._setIconStyles(div, 'icon');

    return div;
  },
  updateColor(newColor) {
    // Update the color option
    this.options.color = newColor;

    // Get the current icon on the map and change its color
    const iconElement = this.getElement();
    if (iconElement) {
      if (this.options.span) {
        const spanElement = iconElement.querySelector('span');
        if (spanElement) {
          spanElement.style.color = newColor;
        }
      } else {
        iconElement.style.color = newColor;
      }
    }
  },
  createShadow() {
    return null;
  },
});

    var LeafletIcon = L.Icon.extend({
        options: {
            iconAnchor: [13, 41],
            popupAnchor: [0, -41]
        }
    });
    var LgreenIcon = new LeafletIcon({
            iconUrl: '/images/marker-green.png',
            iconSize: [26, 41]
        }),
        LredIcon = new LeafletIcon({
            iconUrl: '/images/marker-red.png',
            iconSize: [26, 41]
        }),
        LorangeIcon = new LeafletIcon({
            iconUrl: '/images/marker-orange.png',
            iconSize: [26, 41]
        }),
        //LwarningIcon = new LeafletIcon({iconUrl: '/images/urgent_marker.svg',iconSize: [26, 41]}),
        LwarningIcon = new LeafletIcon({
            iconUrl: '/images/marker_danger.svg',
            iconSize: [35, 41]
        }),
        LSnowPlow1Icon = new LeafletIcon({
            iconUrl: '/images/snowplow1.svg',
            iconSize: [60, 60],
        }),
        LFirefoxIcon = new LeafletIcon({
            iconUrl: 'http://joshuafrazier.info/images/firefox.svg',
            iconSize: [38, 95]
        });
    var selectedItem;
    /*************THIS IS CODE TO USE CUSTOMIZED SVG ICONS FOR MARKERS****************/
    var svgrect =
        "<svg xmlns='http://www.w3.org/2000/svg'><rect x='0' y='0' width='20' height='10' fill='#5a7cd2'></rect><rect x='0' y='15' width='20' height='10' fill='#5d52cf'></rect></svg>";
    var mySvgString =
        '<svg width="866" height="1000" xmlns="http://www.w3.org/2000/svg"><metadata id="metadata1">image/svg+xml</metadata><circle fill="#fee08b" cx="466" cy="532" r="395"/><circle fill="#ffffbf" cx="400" cy="468" r="395"/></svg>';
    /*
        	For data URI SVG support in Firefox & IE it's necessary to URI encode the string
        	& replace the '#' character with '%23'. `encodeURI()` won't do this which is
        	why `replace()` must be used on the string afterwards.
		*/
    var svgurl = encodeURI("data:image/svg+xml," + mySvgString).replace('#', '%23');
    var LSVGIcon = new LeafletIcon({
        iconUrl: svgurl,
        iconSize: [41, 47]
    });
    /*************END CODE TO USE CUSTOMIZED SVG ICONS FOR MARKERS****************/
    //SETTINGS FOR NUMBERED MARKER
    L.NumberedDivIcon = L.Icon.extend({
        options: {
            //	iconUrl: '/images/marker_hole_star_yellow1.svg',
            number: '',
            shadowUrl: '/images/markers_shadow.png',
            shadowAnchor: [10, 12],
            shadowSize: [36, 16],
            //	iconSize: new L.Point(35, 41),
            //	iconAnchor: new L.Point(18, 41),
            popupAnchor: new L.Point(0, -33),
            className: 'leaflet-div-icon2'
        },
        createIcon: function () {
            var div = document.createElement('div');
            var img = this._createImg(this.options['iconUrl']);
            var numdiv = document.createElement('div');
            numdiv.setAttribute("class", "number");
            numdiv.innerHTML = this.options['number'] || '';
            div.appendChild(img);
            div.appendChild(numdiv);
            this._setIconStyles(div, 'icon');
            return div;
        }
    });
    //END SETTINGS FOR NUMBERED MARKER
    var LNumIconDiamondBlack = new L.NumberedDivIcon({
            iconUrl: '/images/marker_hole_diamond_black.svg',
            iconSize: [32, 41],
            iconAnchor: [16, 41],
            number: ''
        }),
        LNumIconGreen = new L.NumberedDivIcon({
            iconUrl: '/images/marker-hole-green.svg',
            iconSize: [25, 41],
            iconAnchor: [13, 41]
        }),
        LNumIconBlue = new L.NumberedDivIcon({
            iconUrl: '/images/marker-hole-blue.svg',
            iconSize: [25, 41],
            iconAnchor: [13, 41]
        }),
        LNumIconStarYellow = new L.NumberedDivIcon({
            iconUrl: '/images/marker_hole_star_yellow1.svg',
            iconSize: [34, 41],
            iconAnchor: [17, 41]
        }),
        LNumIconSquareRed = new L.NumberedDivIcon({
            iconUrl: '/images/marker_hole_square_red.svg',
            iconSize: [30, 41],
            iconAnchor: [15, 41]
        });
    var map = L.map('map', {
        editable: true
    });
    map.doubleClickZoom.disable();
    // variable to track the layer being edited
    var currentlyDeleting = false;
    // track if we should disable custom editing as a result of other actions (create/delete)
    var disableEditing = false;

    var matrix = {
        0: 0.00000228882,
        1: 0.00000457764,
        2: 0.0000091552734375,
        3: 0.000018310546875,
        4: 0.00003662109375,
        5: 0.0000732421875,
        6: 0.000146484375,
        7: 0.00029296875,
        8: 0.0005859375,
        9: 0.001171875,
        10: 0.00234375,
        11: 0.0046875,
        12: 0.009375,
        13: 0.01875,
        14: 0.0375,
        15: 0.075,
        16: 0.15,
        17: 0.3,
        18: 0.6,
        19: 1.2,
        20: 2.4,
        21: 4.8,
        22: 9.6
    };
    // when the map is clicked, stop editing
    var cusrsortype;


    map.on('click', function (e) {
        //		if (e.target.dragging._draggable._newPos === undefined) {
        var millis = Date.now() - lastdragevent;
        if (millis > 60) {

            clearSelected();
            $('#Selected').css("display", "none");
            if (typeof propertyMarker !== "undefined") {
                map.removeLayer(propertyMarker);
            }
            $("#help").show();
            activaTab('Summary');
        }
    });
    map.on('movestart', function (e) {
        $('#Selected').css("display", "none");
    });
    map.on('moveend', function (e) {
        //	displayLegend();
        mapCenter = map.getCenter();
        if (typeof loaded != 'undefined') {
            if (loaded) {
                if (loadedtype == "gmap") {
                    disableeventlistner = true;
                    try {
                        loadedvar.setCenter(mapCenter);
                    } catch (err) {

                    }
                    disableeventlistner = false;
                }
                if (loadedtype == "bbmap") {
                    disableeventlistner = true;
                    try {
                        loadedvar.setView({
                            center: new Microsoft.Maps.Location(mapCenter.lat,
                                mapCenter.lng)
                        });
                    } catch (err) {

                    }
                    disableeventlistner = false;
                }
            }
        }
    });
    // when we start using creation tools disable our custom editing
    map.on('draw:createstart', function () {
        disableEditing = true;

    });
    map.on('load', function () {
        //	initializePresets();
    });
    // when we start using deletion tools, hide attributes and disable custom editing

    map.on('draw:deletestart', function () {
        disableEditing = true;
        currentlyDeleting = true;
    });
    // listen to the draw created event
    map.on('draw:created', function (e) {
        disableEditing = false;
        activaTab('Summary');

    });

    var markerid;
    var globalSelectedGroup = 'none';
    var drawControl;
    var outerShape;
    var donutHole = 0;
    var urlkey = "";
    var presets = {};
    var layerGroup = L.layerGroup().addTo(map);
    var drawnItems = L.featureGroup().addTo(layerGroup);
    var drawnGroups = L.featureGroup().addTo(layerGroup);
    var drawnItemLabels = L.featureGroup().addTo(layerGroup);
    //var drawnItems = L.featureGroup().addTo(map);
    var lastdragevent = Date.now();
    var featureGroups = [];
    var arrowHeads = [];
    var animations = [];
    var onPolyClick;
    var getPopupContent;
    var markers;
    var currentLayer = 'Mapbox';
    var _round;
    var strLatLng;
    var zoomBounds;
    var selectedFeature = null;
    var bid = '0';
    var layerid = '0';
    var address1 = '';
    var address2 = '';
    var city = '';
    var state = '';
    var zip = '';
    var country = 'USA';
    var currentFileName = '';
    var shapeCounter = 0;
    var showlabel = 0;
    var propertyMarker;
    var totalarea = 0;
    var parkinglotarea = 0;
    var sidewalkarea = 0;
    var parkingdeckarea = 0;
    var loadingdockarea = 0;
    var drivewayarea = 0;
    var bedspacearea = 0;
    var turfareaarea = 0;
    var parcelarea = 0;
    var legendColorArray = [];
    var filteredItems = L.featureGroup(); //List of filtered Items
    var saveItems = L.featureGroup();
    var mostRecent = {
        color: '',
        strokeWeight: '',
        fillOpacity: '',
        arrow: '',
        fontSize: '',
        preset: '',
        text: ''
    };
    var savedMapLayers = '';
    var tempshape = '';
    var clone = '';
    var printer;

    var labelLegend;
    var tempLegendArray = [];
    var opensavemode = '';
    var customSearchControl;

    var groupTemplate;
    var layerTemplate;
    var groupPropertiesTemplate;
    var groupEditorTemplate;
    $(document).ready(function () {
        // Compile All Handlebar Templates
        var source = document.getElementById("group-template").innerHTML;
        groupTemplate = Handlebars.compile(source);
        source = document.getElementById("layer-template").innerHTML;
        layerTemplate = Handlebars.compile(source);
        source = document.getElementById("group-properties-template").innerHTML;
        groupPropertiesTemplate = Handlebars.compile(source);

        // End Compilation of Templates


        //			initializePresets();
        (function ($) {
            var oldappend = $.fn.append;
            var count = 0;

        })(jQuery);
        $("#showFillPaletteOnly").spectrum({
            color: "rgb(244, 204, 204)",
            showPaletteOnly: true,
            change: function (color) {
                if (typeof selectedItem !== "undefined") {
                    selectedItem.setStyle({
                        fillColor: color.toHexString()
                    });
                    selectedItem.properties.fillColor = selectedItem.options.fillColor;
                    mostRecent.color = selectedItem.options.fillColor;
                    //	saveLocal();
                }
            },
            hideAfterPaletteSelect: true,
            palette: [
                ["rgb(0, 0, 0)", "rgb(67, 67, 67)", "rgb(102, 102, 102)",
                    "rgb(204, 204, 204)", "rgb(217, 217, 217)",
                    "rgb(255, 255, 255)"
                ],
                ["rgb(152, 0, 0)", "rgb(255, 0, 0)", "rgb(255, 153, 0)",
                    "rgb(255, 255, 0)",
                    "rgb(0, 255, 0)",
                    "rgb(0, 255, 255)", "rgb(74, 134, 232)", "rgb(0, 0, 255)",
                    "rgb(153, 0, 255)", "rgb(255, 0, 255)"
                ],
                ["rgb(230, 184, 175)", "rgb(244, 204, 204)",
                    "rgb(252, 229, 205)",
                    "rgb(255, 242, 204)", "rgb(217, 234, 211)",
                    "rgb(208, 224, 227)", "rgb(201, 218, 248)",
                    "rgb(207, 226, 243)",
                    "rgb(217, 210, 233)", "rgb(234, 209, 220)",
                    "rgb(221, 126, 107)", "rgb(234, 153, 153)",
                    "rgb(249, 203, 156)",
                    "rgb(255, 229, 153)", "rgb(182, 215, 168)",
                    "rgb(162, 196, 201)", "rgb(164, 194, 244)",
                    "rgb(159, 197, 232)",
                    "rgb(180, 167, 214)", "rgb(213, 166, 189)",
                    "rgb(204, 65, 37)", "rgb(224, 102, 102)",
                    "rgb(246, 178, 107)",
                    "rgb(255, 217, 102)", "rgb(147, 196, 125)",
                    "rgb(118, 165, 175)", "rgb(109, 158, 235)",
                    "rgb(111, 168, 220)",
                    "rgb(142, 124, 195)", "rgb(194, 123, 160)",
                    "rgb(166, 28, 0)", "rgb(204, 0, 0)", "rgb(230, 145, 56)",
                    "rgb(241, 194, 50)", "rgb(106, 168, 79)",
                    "rgb(69, 129, 142)", "rgb(60, 120, 216)",
                    "rgb(61, 133, 198)",
                    "rgb(103, 78, 167)", "rgb(166, 77, 121)",
                    "rgb(91, 15, 0)", "rgb(102, 0, 0)", "rgb(120, 63, 4)",
                    "rgb(127, 96, 0)",
                    "rgb(39, 78, 19)",
                    "rgb(12, 52, 61)", "rgb(28, 69, 135)", "rgb(7, 55, 99)",
                    "rgb(32, 18, 77)",
                    "rgb(76, 17, 48)"
                ]
            ]
        });
        $("#showPaletteOnly").spectrum({
            color: "rgb(244, 204, 204)",
            showPaletteOnly: true,
            change: function (color) {
                if (typeof selectedItem !== "undefined") {
                    selectedItem.setStyle({
                        color: color.toHexString()
                    });
                    selectedItem.properties.color = selectedItem.options.color;
                    mostRecent.color = selectedItem.options.color;
                    //	saveLocal();
                }
            },
            hideAfterPaletteSelect: true,
            palette: [
                ["rgb(0, 0, 0)", "rgb(67, 67, 67)", "rgb(102, 102, 102)",
                    "rgb(204, 204, 204)", "rgb(217, 217, 217)",
                    "rgb(255, 255, 255)"
                ],
                ["rgb(152, 0, 0)", "rgb(255, 0, 0)", "rgb(255, 153, 0)",
                    "rgb(255, 255, 0)",
                    "rgb(0, 255, 0)",
                    "rgb(0, 255, 255)", "rgb(74, 134, 232)", "rgb(0, 0, 255)",
                    "rgb(153, 0, 255)", "rgb(255, 0, 255)"
                ],
                ["rgb(230, 184, 175)", "rgb(244, 204, 204)",
                    "rgb(252, 229, 205)",
                    "rgb(255, 242, 204)", "rgb(217, 234, 211)",
                    "rgb(208, 224, 227)", "rgb(201, 218, 248)",
                    "rgb(207, 226, 243)",
                    "rgb(217, 210, 233)", "rgb(234, 209, 220)",
                    "rgb(221, 126, 107)", "rgb(234, 153, 153)",
                    "rgb(249, 203, 156)",
                    "rgb(255, 229, 153)", "rgb(182, 215, 168)",
                    "rgb(162, 196, 201)", "rgb(164, 194, 244)",
                    "rgb(159, 197, 232)",
                    "rgb(180, 167, 214)", "rgb(213, 166, 189)",
                    "rgb(204, 65, 37)", "rgb(224, 102, 102)",
                    "rgb(246, 178, 107)",
                    "rgb(255, 217, 102)", "rgb(147, 196, 125)",
                    "rgb(118, 165, 175)", "rgb(109, 158, 235)",
                    "rgb(111, 168, 220)",
                    "rgb(142, 124, 195)", "rgb(194, 123, 160)",
                    "rgb(166, 28, 0)", "rgb(204, 0, 0)", "rgb(230, 145, 56)",
                    "rgb(241, 194, 50)", "rgb(106, 168, 79)",
                    "rgb(69, 129, 142)", "rgb(60, 120, 216)",
                    "rgb(61, 133, 198)",
                    "rgb(103, 78, 167)", "rgb(166, 77, 121)",
                    "rgb(91, 15, 0)", "rgb(102, 0, 0)", "rgb(120, 63, 4)",
                    "rgb(127, 96, 0)",
                    "rgb(39, 78, 19)",
                    "rgb(12, 52, 61)", "rgb(28, 69, 135)", "rgb(7, 55, 99)",
                    "rgb(32, 18, 77)",
                    "rgb(76, 17, 48)"
                ]
            ]
        });


        $('body').on('click', 'a.changeMarker', function () {
            if (typeof selectedItem !== "undefined") {
                if (selectedItem.feature.properties.type == 'Marker') {
                    //	myicons.Markers.type[i].icons[j].iconurl
                    var i = eval($(this).data("type"));
                    var j = eval($(this).data("icon"));
                    var icon = new LeafletIcon({
                        iconUrl: myicons.Markers.type[i].icons[j].iconurl,
                        iconSize: myicons.Markers.type[i].icons[j].iconsize
                    });
                    oldpreset = iconURL2GroupName(selectedItem.feature.properties.markerURL);
                    presets[oldpreset.shortname].leafletlayer.removeLayer(selectedItem);
                    selectedItem.setIcon(icon);
                    selectedItem.feature.properties.markerURL = selectedItem.options.icon.options
                        .iconUrl;
                    selectedItem.feature.properties.markerSize = selectedItem.options.icon.options
                        .iconSize;
                    obj = iconURL2GroupName(selectedItem.feature.properties.markerURL);
                    if (obj) {
                        preset = addMarkerPreset(obj.shortname, obj.name);
                        preset.leafletlayer.addLayer(selectedItem);
                    } else
                        presets["markers"].leafletlayer.addLayer(selectedItem);
                    $("#cellIcon").attr("src", selectedItem.feature.properties.markerURL);
                    // this is a fix for a bug in leaflet draw that was toggling the edit rectangle on the markers when setting them to editable
                    L.DomUtil.addClass(selectedItem._icon,
                        'leaflet-edit-marker-selected');
                    icon = selectedItem._icon;
                    offset = 4;
                    iconMarginTop = parseInt(icon.style.marginTop, 10) - offset;
                    iconMarginLeft = parseInt(icon.style.marginLeft, 10) - offset;
                    selectedItem._icon.style.marginTop = iconMarginTop + 'px';
                    selectedItem._icon.style.marginLeft = iconMarginLeft + 'px';
                    //end of fix for bug
                } else if (selectedItem.feature.properties.type == 'Photo') {
                    selectedItem.setIcon(eval($(this).data("id")));
                    selectedItem.setIcon(new L.NumberedDivIcon({
                        iconUrl: selectedItem.options.icon.options.iconUrl,
                        iconSize: selectedItem.options.icon.options.iconSize,
                        number: selectedItem.feature.properties.number
                    }));
                    selectedItem.feature.properties.markerURL = selectedItem.options.icon.options
                        .iconUrl;
                    selectedItem.feature.properties.markerSize = selectedItem.options.icon.options
                        .iconSize;
                    obj = photoURL2GroupName(selectedItem.feature.properties.markerURL);
                    if (obj) {
                        preset = addPhotoPreset(obj.shortname, obj.name);
                        preset.leafletlayer.addLayer(selectedItem);
                    } else
                        presets["photos"].leafletlayer.addLayer(selectedItem);
                    $("#cellIcon").attr("src", selectedItem.feature.properties.markerURL);
                    // this is a fix for a bug in leaflet draw that was toggling the edit rectangle on the markers when setting them to editable
                    L.DomUtil.addClass(selectedItem._icon,
                        'leaflet-edit-marker-selected');
                    var icon = selectedItem._icon;
                    var offset = 4;
                    var iconMarginTop = parseInt(icon.style.marginTop, 10) - offset,
                        iconMarginLeft = parseInt(icon.style.marginLeft, 10) -
                        offset;
                    selectedItem._icon.style.marginTop = iconMarginTop + 'px';
                    selectedItem._icon.style.marginLeft = iconMarginLeft + 'px';
                    //end of fix for bug
                } else if (selectedItem.properties.type == 'Line') {
                    var i = eval($(this).data("type"));
                    var j = eval($(this).data("icon"));
                    selectedItem.feature.properties.AnimIcon = myicons.Animation.type[i].icons[j].iconurl;
                    $("#cellAnimateIcon").attr("src", selectedItem.properties.AnimIcon);
                    // this is a fix for a bug in leaflet draw that was toggling the edit rectangle on the markers when setting them to editable
                }
            }
            //	saveLocal();
        });
        map.fitBounds(mapbounds);
        let isChromeOrEdge = /Chrome\//.test(navigator.userAgent) || /Edg\//.test(navigator.userAgent);
        if (isChromeOrEdge) {
            
            let originalMapBounds = mapbounds;
            
            
            setTimeout(function() {
                map.fitBounds(originalMapBounds);
            }, 100);
        }
        if (map.getZoom() > 21) {
            map.setZoom(19);
        }
        //addPhotoMarkers();
        //$( "#map" ).css({'top' : 57 + 'px'});

        var osmUrl = 'http://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
            osmAttrib =
            '&copy; <a href="http://openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            osm = L.tileLayer(osmUrl, {
                maxZoom: 22,
                maxNativeZoom: 18,
                attribution: osmAttrib
            }),
            mapLink = '<a href="http://www.esri.com/">Esri</a>',
            wholink =
            'i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community';
        var mapbox = L.tileLayer(
            '/crux/mapbox/styles/v1/mapbox/satellite-streets-v11/tiles/{z}/{x}/{y}', {
                attribution: 'Mapbox',
                maxZoom: 22,
                maxNativeZoom: 19,
                tileSize: 512,
                zoomOffset: -1
            });
        var layercontrol = L.control.layers({
             "Google Satellite": L.tileLayer(
                'https://www.google.com/maps/vt?lyrs=s@189&gl=cn&x={x}&y={y}&z={z}', {
                    attribution: 'google',
                    maxZoom: 22,
                    maxNativeZoom: 19
                }).addTo(map),
            "Mapbox": mapbox,
        }, {}, {
            position: "bottomleft"
        }).addTo(map)
        //	toggle.addTo(map);
        //	toggle.setPosition("bottomleft");



        var LassoControl = L.Control.extend({
            options: {
                position: 'topleft',
                html: '\\/\\'
            },
            lasso: null,
            lassoActive: false,
            lassoFeatures: null,
            controlBar: null,
            data: null,

            onAdd: function (map) {
                var container = L.DomUtil.create('div', 'leaflet-control leaflet-bar'),
                    link = L.DomUtil.create('a', '', container);

                link.href = '#';
                link.title = 'Select Multiple Shapes';
                link.innerHTML = this.options.html;

                this.controlBar = L.control.bar('multiselectbar', {
                    position: 'top',
                    visible: false
                });
                map.addControl(this.controlBar);

                L.DomEvent.addListener(container, 'click', this._startLasso, this);



                return container;
            },
            _startLasso: function (e) {
                L.DomEvent.stopPropagation(e);

                this.lassoActive = true;

                clearSelected();

                $('#Selected').css("display", "none");

                this.lasso = map.selectAreaFeature.enable();
                L.DomEvent.addListener(map, 'pathchange', this._lassoChange, this);
            },
            _lassoChange: function (e) {
                this.lassoFeatures = this.lasso.getFeaturesSelected('all');
                map.selectAreaFeature.disable();
                if (this.lassoFeatures != null) {
                    this.data = this._processSelectedLayers();
                    $("#multiselectbar #numFeatures").html(this.data.layers.length);
                    if (!this.data.sameGroup) {
                        $("#multiselectbar #changeGroupPresets").hide();
                    } else {

                        if (presets[this.data.groupName].photogroup) {
                            $("#changeGroupPresets").empty();
                            for (var property in presets) {

                                if (presets[property].photogroup && property != "photos")
                                    $("#changeGroupPresets").append("<option value='" +
                                        property + "'>" + presets[property].name +
                                        "</option>");
                            }

                        } else if (presets[this.data.groupName].textgroup) {
                            $("#changeGroupPresets").empty();
                            for (var property in presets) {

                                if (presets[property].textgroup && property != "text")
                                    $("#changeGroupPresets").append("<option value='" +
                                        property + "'>" + presets[property].name +
                                        "</option>");
                            }
                        } else if (presets[this.data.groupName].markergroup) {
                            $("#changeGroupPresets").empty();
                            for (var property in presets) {

                                if (presets[property].markergroup && property != "markers")
                                    $("#changeGroupPresets").append("<option value='" +
                                        property + "'>" + presets[property].name +
                                        "</option>");
                            }
                        } else if (presets[this.data.groupName].vectorgroup) {
                            $("#changeGroupPresets").empty();
                            for (var property in presets) {


                                if (presets[property].vectorgroup && property != "none" &&
                                    property != "hole") {

                                    $("#changeGroupPresets").append("<option value='" +
                                        property + "'>" + presets[property].name +
                                        "</option>");
                                }
                            }

                        }
                        var k = L.DomUtil.get("changeGroupPresets");
                        L.DomEvent.addListener(k, 'change', this._changeGroup, this);
                        $("#multiselectbar #changeGroupPresets").show();

                    }
                    var p = L.DomUtil.get("deleteAllFeatures")
                    var d = L.DomUtil.get("closeMultiSelectBar")
                    var m = L.DomUtil.get("continueDrawing")
                    L.DomEvent.addListener(p, 'click', this._deleteAllFeatures, this);
                    L.DomEvent.addListener(d, 'click', this._closeMultiSelectBar, this);
                    L.DomEvent.addListener(m, 'click', this._continueDrawing, this);

                    this.controlBar.show();

                    //map.selectAreaFeature.disable();
                } else {
                    map.selectAreaFeature.removeAllArea();
                    map.selectAreaFeature.disable();
                    this.lasso = null;
                    $.snackbar({
                        content: "No features Selected. Please try again."
                    });
                }
            },
            _continueDrawing() {
                map.selectAreaFeature.enable();
            },
            _closeMultiSelectBar() {
                map.selectAreaFeature.removeAllArea();
                map.selectAreaFeature.disable();
                this.lasso = null;
                this.controlBar.hide();

            },
            _changeGroup(e) {
                var grp = $("#changeGroupPresets").val();
                for (var i = 0; i < this.data.layers.length; i++) {
                    var groupID = drawnGroups.searchLayerParent(this.data.layers[i]._leaflet_id).metadata
                        .shortname;
                    presets[groupID].leafletlayer.removeLayer(this.data.layers[i]);

                    presets[grp].leafletlayer.addLayer(this.data.layers[i]);
                    this.data.layers[i].setStyle(
                        presets[grp].leafletlayer.metadata
                    );

                    this.data.layers[i].feature.properties.preset = grp;
                }
                map.selectAreaFeature.removeAllArea();
                map.selectAreaFeature.disable();
                this.lasso = null;
                this.controlBar.hide();
            },
            _deleteAllFeatures() {
                var result = confirm("Are you sure you want to delete all selected features?");
                if (result) {

                    for (var i = 0; i < this.data.layers.length; i++) {
                        var groupID = drawnGroups.searchLayerParent(this.data.layers[i]._leaflet_id)
                            .metadata.shortname;
                        presets[groupID].leafletlayer.removeLayer(this.data.layers[i]);

                    }
                    map.selectAreaFeature.removeAllArea();
                    map.selectAreaFeature.disable();
                    this.lasso = null;
                    this.controlBar.hide();
                }
            },
            _processSelectedLayers() {
                //Remove Duplicates
                var sameGroup = true;
                var k = this.lassoFeatures.filter(function (item, index, arr) {
                    return arr.indexOf(item) == index;
                });
                var i = k.length;
                while (i--) {
                    if (k[i].options !== undefined) {
                        if (k[i].options.type !== undefined) {
                            if (k[i].options.type == "lassoselect") {
                                k.splice(i, 1);
                            }
                        }
                    }
                }
                var group = drawnGroups.searchLayerParent(k[0]._leaflet_id).metadata.shortname;
                for (i = 1; i < k.length; i++) {
                    var thisgroup = drawnGroups.searchLayerParent(k[i]._leaflet_id).metadata.shortname;
                    if (thisgroup != group)
                        sameGroup = false;
                }
                return {
                    "layers": k,
                    "sameGroup": sameGroup,
                    "groupName": group
                }
            },
            _mapClick: function (e) {

                return;
            }
        });
        //map.addControl(new LassoControl());



        //ADD THE DRAW CONTROL
        drawControl = new L.Control.Draw({
            draw: {
                polyline: {
                    metric: false,
                    shapeOptions: {
                        opacity: 1,
                        color: '#ffff00' //,
                        //dashArray: '20,15',
                        //lineJoin: 'round'
                    }
                },
                polygon: {

                    shapeOptions: {
                        opacity: 0.9,

                        weight: 2,
                        fillOpacity: 0.3 //,
                        //dashArray: '20,15',
                        //lineJoin: 'round'
                    },
                    allowIntersection: false,
                    drawError: {
                        color: 'orange',
                        timeout: 1000
                    },
                    showArea: true,
                    metric: false
                },
                rectangle: {
                    shapeOptions: {
                        color: '#00ff00',
                        opacity: 0.9,
                        weight: 2,
                        fillOpacity: 0.3
                    },
                    showArea: true,
                    metric: false
                },
                circle: {
                    shapeOptions: {
                        color: '#00ff00',
                        opacity: 0.9,
                        weight: 2,
                        fillOpacity: 0.3
                    },
                    showRadius: false,
                    metric: false
                },
                marker: {
                    icon: LorangeIcon
                }
            }
        });
        //Add draw control to map
        //map.addControl(drawControl);
        /*

					L.control.custom({
					position: 'bottomleft',
					content : '<button class="btn btn-default btn-sm" id="changeImg">'+
					'    <i class="fa fa-refresh"></i> Change Image'+
					'</button>',
					classes : '',
					style   :
					{
                    margin: '0px 0px',
                    padding: '0px',
					},
					})
					.addTo(map);

				*/
        //alert(imglist.length);

 
        var LegendControl = L.Control.extend({
            options: {
                position: 'bottomright',
                legends: presets,
                showlegend: true

            },
            legenditems: 0,
            onAdd: function (map) {
                this._map = map;
                this._container = L.DomUtil.create('div',
                    'leaflet-control leaflet-bar leaflet-legend-control');
                if (this.options.showlegend) {
                    //	this.render();
                    L.DomEvent.addListener(this._map, 'layeradd', this.render, this);
                    L.DomEvent.addListener(this._map, 'layerremove', this.render, this);
                    L.DomEvent.addListener(this._map, 'updatelegend', this.render, this);
                    L.DomEvent.addListener(this._map, 'initlegend', this._initLegend, this);
                    //				this._map.on('layeradd',this.render(), this);
                    //				this._map.on('layerremove', this.render(), this);
                }
                return this._container;
            },
            _initLegend: function () {
                this.options.legends = presets;
                this.render();
            },
            render: function () {
                L.DomUtil.empty(this._container);
                this.legenditems = 0;
                for (var legend in this.options.legends) {
                    this._renderLegend(this.options.legends[legend]);
                }
                if (!this.legenditems)
                    this._container.style = "display: none";
                else
                    this._container.style = "display: block";
            },
            _renderLegend(legend) {
                if (legend.leafletlayer.metadata.shortname == "none" || legend.leafletlayer
                        .metadata
                        .shortname ==
                    "hole") {

                    return;
                }
                if (legend.leafletlayer.metadata.photogroup || legend.leafletlayer.metadata
                    .textgroup)
                    return;
                if (!legend.leafletlayer.getLayers().length)
                    return;
                if (!legend.leafletlayer.metadata.groupVisible)
                    return;
                if (!legend.leafletlayer.metadata.legendVisible)
                    return;

                const block = L.DomUtil.create('div', 'legend-block', this._container);

                const spancolor = L.DomUtil.create('span', 'legend-symbol csssquare', block);
                //console.log(legend.leafletlayer._layers[Object.keys(legend.leafletlayer._layers)[0]]._icon.currentSrc)
                if (legend.leafletlayer.metadata.markergroup) {
                    var temp = legend.leafletlayer._layers[Object.keys(legend.leafletlayer._layers)[0]]._icon;
                    if(temp == null)
                        temp = legend.leafletlayer._layers[Object.keys(legend.leafletlayer._layers)[0]].feature.properties.markerURL
                    else
                        temp = temp.currentSrc
                    spancolor.style = "background-image: url(" + temp +
                        ");background-repeat:no-repeat;background-size:contain;border:transparent;background-position:center;";
                } else
                    spancolor.style = "background-color: " + legend.leafletlayer.metadata.fillColor;
                const spanlabel = L.DomUtil.create('span', 'legend-label', block);
                spanlabel.innerHTML = legend.leafletlayer.metadata.name;
                this.legenditems++;


            },
        });
        map.addControl(new LegendControl());
  
        // Truncate value based on number of decimals
        _round = function (num, len) {
            return Math.round(num * (Math.pow(10, len))) / (Math.pow(10, len));
        };
        // Helper method to format LatLng object (x.xxxxxx, y.yyyyyy)
        strLatLng = function (latlng) {
            return "(" + _round(latlng.lat, 6) + ", " + _round(latlng.lng, 6) + ")";
        };
        // Generate popup content based on layer type
        // - Returns HTML string, or null if unknown object
        var image1 = '<img src="/images/marker-green.png">';
        var image2 = '<img src="/images/marker-red.png">';
        var image3 = '<img src="/images/marker-orange.png">';
        var image4 = '<img src="/images/marker-truck-yellow.png">';
        var image5 =
            '<img src="/images/urgent_marker.svg" width="26" height="41">';
        var image6 =
            '<img src="/images/snowplow1.svg" width="41" height="41" style="-ms-transform: rotate(90deg); /* IE 9 */-webkit-transform: rotate(90deg); /* Chrome, Safari, Opera */transform: rotate(90deg);">';
        var image7 =
            '<img src="http://joshuafrazier.info/images/firefox.svg" width="13" height="32">';

        function addEvent(el, type, handler) {
            if (el.attachEvent) el.attachEvent('on' + type, handler);
            else el.addEventListener(type, handler);
        }

        function removeEvent(el, type, handler) {
            if (el.detachEvent) el.detachEvent('on' + type, handler);
            else el.removeEventListener(type, handler);
        }
 

        //ADD PHOTO MARKERS
        if (imglist.length == 0) {
            $("#tab_photos").hide();
        }
        markers = new L.FeatureGroup();
        for (i = 0; i < imglist.length; i++) {
            var bldgicon = LgreenIcon;
            //numBldgs = numBldgs + 1;
            //to determine which icon to use, check the tags
            var markerUrl, markerSize;
            var tags = imglist[i][9];
            markerUrl = LNumIconBlue.options.iconUrl;
            markerSize = LNumIconBlue.options.iconSize;
            if (tags.length > 0) {
                if (tags.indexOf('pre') !== -1) {
                    markerUrl = LNumIconSquareRed.options.iconUrl;
                    markerSize = LNumIconSquareRed.options.iconSize;
                } else if (tags.indexOf('hazard') !== -1) {
                    markerUrl = LNumIconDiamondBlack.options.iconUrl;
                    markerSize = LNumIconDiamondBlack.options.iconSize;
                } else if (tags.indexOf('new') !== -1) {
                    markerUrl = LNumIconStarYellow.options.iconUrl;
                    markerSize = LNumIconStarYellow.options.iconSize;
                } else if (tags.indexOf('post') !== -1) {
                    markerUrl = LNumIconGreen.options.iconUrl;
                    markerSize = LNumIconGreen.options.iconSize;
                }
            } else {
                markerUrl = LNumIconBlue.options.iconUrl;
                markerSize = LNumIconBlue.options.iconSize;
            }
            addPhotoMarkers(imglist[i][3], imglist[i][1], imglist[i][2], imglist[i][9],
                markerUrl,
                markerSize, imglist[i][5], imglist[i][6], imglist[i][7], imglist[i][8], imglist[i][4]);
        }
        map.on('baselayerchange', function (e) {
            currentLayer = e.name;
        });

 
        $.fn.fitText = function (kompressor, options) {
            // Setup options
            var compressor = 1.5 || 1,
                settings = $.extend({
                    'minFontSize': Number.NEGATIVE_INFINITY,
                    'maxFontSize': Number.POSITIVE_INFINITY
                }, options);
            return this.each(function () {
                // Store the object
                var $this = $(this);
                // Resizer() resizes items based on the object width divided by the compressor * 10
                var resizer = function () {
                    s = Math.max(Math.min($this.width() / (compressor * 10),
                        parseFloat(settings.maxFontSize)),
                        parseFloat(settings.minFontSize))
                    $this.css('font-size', s);

                    if(s<=4)
                        $this.css('display','none')
                };
                // Call once to set.
                resizer();
                // Call on resize. Opera debounces their resize by default.
                $(window).on('resize.fittext orientationchange.fittext', resizer);
            });
        };
        initializePresets();
    }); //END DOCUMENT READY
    map.on('popupopen', function (e) {
        var polygon = e.popup._source;
        var marker = this;
    });

    function addArrowhead(arrow, status) {
        if (status == true) {
            //CHECK TO SEE IF THERE IS ALREADY AN ANIMATION FOR THIS LINE
            var already_an_arrow = 0;
            for (i = 0; i < arrowHeads.length; i++) {
                if (arrowHeads[i][0] == arrow._leaflet_id) {
                    already_an_arrow = 1;
                }
            }
            var existsindrawn = false;
            if (already_an_arrow == 0) {
                var arrowHead = L.polylineDecorator(arrow, {
                    patterns: [{
                        offset: '100%',
                        repeat: 0,
                        symbol: L.Symbol.arrowHead({
                            pixelSize: 15,
                            polygon: false,
                            pathOptions: {
                                color: arrow.feature.properties.color,
                                stroke: true
                            }
                        })
                    }]
                }).addTo(map);
                arrow.feature.properties.arrow = 'right';
                arrowHeads.push([arrow._leaflet_id, arrowHead._leaflet_id, arrowHead]);
            }
        } else {
            for (i = 0; i < arrowHeads.length; i++) {
                if (arrowHeads[i][0] == arrow._leaflet_id) {
                    map.removeLayer(arrowHeads[i][2]);
                    arrowHeads.splice(i, 1);
                }
            }
            arrow.properties.arrow = 'none';
        }
    }

    function addPolylineAnimation(arrow, status, src) {
        if (status == true) {
            //CHECK TO SEE IF THERE IS ALREADY AN ANIMATION FOR THIS LINE
            var already_an_arrow = 0;
            for (i = 0; i < animations.length; i++) {
                if (animations[i][0] == arrow._leaflet_id) {
                    already_an_arrow = 1;
                }
            }
            var existsindrawn = false;
            /*	drawnItems.eachLayer(function (layer) {
            		if (layer._leaflet_id == arrow._leaflet_id) {
            			existsindrawn = true;
            		}
            	}); */
            if (already_an_arrow == 0) {
                //THIS ADDS AN ANIMATED SNOWPLOW TO THE LINE
                var arrowHead = L.polylineDecorator(arrow).addTo(layerGroup);
                var matrix2 = {
                    1: 0.125,
                    2: 0.125,
                    3: 0.125,
                    4: 0.125,
                    5: 0.125,
                    6: 0.125,
                    7: 0.125,
                    8: 0.125,
                    9: 0.125,
                    10: 0.125,
                    11: 0.125,
                    12: 0.20,
                    13: 0.30,
                    14: 0.40,
                    15: 0.50,
                    16: 0.60,
                    17: 0.70,
                    18: 0.80,
                    19: 0.90,
                    20: 1,
                    21: 1.25,
                    22: 1.50
                };
                var arrowOffset = 0;
                var anim = window.setInterval(function () {
                    currentZoom = map.getZoom();
                    iconx = 36 - ((20 - currentZoom) * 5);
                    icony = 108 - ((20 - currentZoom) * 15);
                    iconancx = 36 - ((20 - currentZoom) * 5);
                    iconanxy = 46 - ((20 - currentZoom) * 5)
                    arrowHead.setPatterns([
                        // {offset: arrowOffset+'%', repeat: 0, symbol: L.Symbol.arrowHead({pixelSize: 15, polygon: false, pathOptions: {stroke: true}})}
                        {
                            offset: arrowOffset + '%',
                            repeat: 0,
                            symbol: L.Symbol.marker({
                                rotate: true,
                                markerOptions: {
                                    icon: L.icon({
                                        zoom: false,
                                        iconUrl: arrow.feature.properties.AnimIcon,
                                        className: "allAnim",
                                        iconSize: [iconx, icony],
                                        iconAnchor: [iconancx, iconanxy]
                                    })
                                }
                            })
                        }
                    ])
                    if (++arrowOffset > 100)
                        arrowOffset = 0;
                }, 100);
                //Add the arrowhead to the arrowhead array
                animations.push([arrow._leaflet_id, arrowHead._leaflet_id, arrowHead]);
            }
        } else {
            for (i = 0; i < animations.length; i++) {
                if (animations[i][0] == arrow._leaflet_id) {
                    map.removeLayer(animations[i][2]);
                    animations.splice(i, 1);
                }
            }
        }
        //alert(animations);
        //END ANIMATION
    }

    function updateDefaultShapeColor(shapeColor) {
        drawControl.setDrawingOptions({
            polygon: {
                shapeOptions: {
                    color: shapeColor,
                    opacity: 0.9,
                    weight: 2
                }
            }
        });
    }

    //just removing for test, can be restored if it breaks anything else
    function addTextAreaOld(text = 'Click to Edit Text<br>Drag to Move Text', location = map.getCenter(),
        fontSize = 9, layerproperties) {
        //TEXT LABELS
        var htmlp = text;
        let tempText = text;
        tempText = tempText?.replace(/(?:\r\n|\r|\n)/g, '<br>').replace(/ /g, '&nbsp;');
		if (typeof layerproperties != 'undefined') {
			if (typeof layerproperties.desctype !== "undefined") {
				if (layerproperties.desctype == 'none')
					htmlp = tempText;
				else {
					var obj = JSON.parse(layerproperties.description);

					temphtml = "<table class='textgrid'>"

					for (var k in obj) {
						if (obj.hasOwnProperty(k)) {
							temphtml += '<tr><td>' + k + '</td><td>' + obj[k] +
								'</td></tr>';
						}
					}
					temphtml += "</table>";
					htmlp = temphtml;

				}
			} else {
				htmlp = tempText;
			}
		}


		var k = [(fontSize / 2) * 50 * matrix[map.getZoom()], (fontSize / 2) * 20 * matrix[map.getZoom()]];
        var label = new L.Marker(location, {
                icon: new CustomDivIcon({
                    className: 'allTextarea',
                    html: htmlp,
                    iconSize: k,
                    iconAnchor: [0, 0],
                    span: true,
                    textColor: layerproperties?.textColor ?? '#FFFFFF',
                    background: layerproperties?.background ?? false,
                    spanClassName: 'allTextAreaSpan',
                }),
                draggable: 'true'
            });

		var layer = label;
		var feature = layer.feature = layer.feature || {};
		feature.type = "Feature";
		feature.properties = feature.properties || {};
		layer.feature.properties.myId = 'This is myId';
		layer.feature.properties.type = 'Text';
		layer.feature.properties.fontSize = fontSize;
		if (typeof layerproperties.desctype !== "undefined")
			layer.feature.properties.desctype = layerproperties.desctype
		if (typeof layerproperties.descrows !== "undefined")
			layer.feature.properties.descrows = layerproperties.descrows
		layer.feature.properties.description = htmlp;
		shapeCounter += 1;
		layer.feature.properties.id = shapeCounter;
		layer.feature.properties.name = "Text-" + shapeCounter;

		presets['textinternal'].leafletlayer.addLayer(layer);
		//layer.on('click', onPolyClick);
		//END TEXT LABELS
		$(".allTextarea").fitText();




        layer.on('dragstart', function (event) {

                event.target.dragging.disable();
            }

        )

    }
    function addTextArea(text = 'Click to Edit Text<br>Drag to Move Text', location = map.getCenter(),
        fontSize = 9, layerproperties) {
        //TEXT LABELS
        var htmlp = text;
		if (typeof layerproperties != 'undefined') {
			if (typeof layerproperties.desctype !== "undefined") {
				if (layerproperties.desctype == 'none')
					htmlp = text;
				else {
					var obj = JSON.parse(layerproperties.description);

					temphtml = "<table class='textgrid'>"

					for (var k in obj) {
						if (obj.hasOwnProperty(k)) {
							temphtml += '<tr><td>' + k + '</td><td>' + obj[k] +
								'</td></tr>';
						}
					}
					temphtml += "</table>";
					htmlp = temphtml;

				}
			} else {
				htmlp = text;
			}
		}


		var k = [(fontSize / 2) * 50 * matrix[map.getZoom()], (fontSize / 2) * 20 * matrix[map.getZoom()]];
		var label = new L.Marker(location, {
			icon: new L.DivIcon({
				className: 'allTextarea',
				html: htmlp,
				iconSize: k,
				iconAnchor: [0, 0],
			}),
			draggable: 'true'
		});

		var layer = label;
		var feature = layer.feature = layer.feature || {};
		feature.type = "Feature";
		feature.properties = feature.properties || {};
		layer.feature.properties.myId = 'This is myId';
		layer.feature.properties.type = 'Text';
		layer.feature.properties.fontSize = fontSize;
		if (typeof layerproperties.desctype !== "undefined")
			layer.feature.properties.desctype = layerproperties.desctype
		if (typeof layerproperties.descrows !== "undefined")
			layer.feature.properties.descrows = layerproperties.descrows
		layer.feature.properties.description = text;
		shapeCounter += 1;
		layer.feature.properties.id = shapeCounter;
		layer.feature.properties.name = "Text-" + shapeCounter;

		presets['textinternal'].leafletlayer.addLayer(layer);
		//layer.on('click', onPolyClick);
		//END TEXT LABELS
		$(".allTextarea").fitText();




        layer.on('dragstart', function (event) {

                event.target.dragging.disable();
            }

        )

    }
    function rgb2hex(rgb) {
        rgb = rgb.match(/^rgba?[\s+]?\([\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?/i);
        return (rgb && rgb.length === 4) ? "#" +
            ("0" + parseInt(rgb[1], 10).toString(16)).slice(-2) +
            ("0" + parseInt(rgb[2], 10).toString(16)).slice(-2) +
            ("0" + parseInt(rgb[3], 10).toString(16)).slice(-2) : '';
    }
    $('button').click(function () {
        var hex = rgb2hex($('input').val());
        $('.result').html(hex);
    });
</script>
<style>
    .allTextarea {
        color: white;
		line-height: 1.1;
		text-shadow: -1px -1px 0 #000,
			1px -1px 0 #000,
			-1px 1px 0 #000,
			1px 1px 0 #000;
            pointer-events: none!important;
        }
        .textgrid {
		background-color: white;
		text-shadow: none;
		color: black;
		font-weight: normal;
        
	}

	.textgrid tr td:first-child {
		font-weight: 600;
	}

	.textgrid tr td:last-child {
		color: grey;
	}

	.textgrid tr td {
		padding: 0.2em;
		padding-right: 0.5em;
	}

	.textgrid tr:nth-child(even) {background-color: #f2f2f2;}
	.textgrid tr:nth-child(odd) {background-color: #ffffff;}

	.textgrid table {
	  border-collapse: collapse;
	}

	.textgrid table, th, td {
	  border: 1px solid black;
	}

    </style>
<!--SET THE BOUNDS-->
<script class="extract-script">
    var lats = [];
    var lngs = [];
    var mapbounds;
    var numBldgs = 0;
    if (imglist.length > 0) {
        if (imglist.length == 1) {
            var sw = new L.LatLng(imglist[0][5], imglist[0][6]);
            map.panTo(sw);
           // map.setZoom(20);
            mapbounds = map.getBounds();
        } else {
            for (i = 0; i < imglist.length; i++) {
                // add the marker coordinates to the lats and lngs arrays
                lats.push(imglist[i][5]);
                lngs.push(imglist[i][6]);
                if (i > (Number(imglist.length) - 2)) {
                    setbounds(map, lats, lngs);
                }
            }
        }
    } else {
        lats.push('39.605796');
        lngs.push('-104.904242');
        lats.push('39.603341');
        lngs.push('-104.901356');
        setbounds(map, lats, lngs);

    }

    function setbounds(map, lats, lngs) {
        var maxlat = Math.max.apply(Math, lats);
        var maxlng = Math.max.apply(Math, lngs);
        var minlat = Math.min.apply(Math, lats);
        var minlng = Math.min.apply(Math, lngs);
        var sw = new L.LatLng(minlat, minlng);
        var ne = new L.LatLng(maxlat, maxlng);
        mapbounds = new L.LatLngBounds(sw, ne);
        //map.locate({setView: true, maxZoom: 18});
    }
</script>
<!--END OF SET THE BOUNDS-->
<!--ADD THE MARKERS-->
<script class="extract-script">
    function addNumberedMarker() {
        var counter = 0;
        var center = map.getCenter();
        var lat = center.lat;
        var lng = center.lng;
        for (i = 0; i < 10; i++) {
            addMarkerWithTextIcon(lat, lng);
            lat -= .0002;
            lng += .0002;
        }

        function addMarkerWithTextIcon() {
            var icon = new L.TextIcon({
                text: ++counter,
                color: 'red'
            });
            icon.options.shadowSize = [0, 0];
            // var marker = L.marker([lat, lng], {id:counter  , icon: icon, draggable:'true'});
            var marker = L.marker([lat, lng], {
                id: counter,
                icon: icon
            });
            // var marker = new L.Marker([lat, lng], drawControl.options.marker).addTo(drawnItems);
            marker.options.icon.setColor('green');
            //var polygon = new L.Polygon(newone, drawControl.options.polygon).addTo(drawnItems);
            //this line sets the background of the numbered markers to transparent or else there would be a white background
            drawnItems.addLayer(marker);
            $('.icon-text').parents('div.leaflet-div-icon').css({
                'background': 'transparent'
            });
        }
    }
    var globalSave;

    function openMapLayer(layer) {
        currentFileName = $("#file-open-name").val();
        var result = mylayers;

        // importv1Format(result);

        // openv2format(JSON.parse(result));
        // layerid = LayerID;

        if (result === Object(result)) {
            importv1Format(result);
        } else {
            result = JSON.parse(result);
            openv2format(result);
        }
        currentFileName = "";

        $("#help").show();
        $("#rowAddPreset").hide();
        $("#text_shapetype").hide();
        activaTab('Summary');

        //initializePresetsOpen();
        //populate the file name displayed to the user

        $("#currentFileName").text(currentFileName);
        //   saveLocal();


        if (map.getZoom() > 18) {
            map.setZoom(18);
        }
    }
  
</script>
<!--END OF ADD THE MARKERS-->

<script class="extract-script">


    function activaTab(tab) {
        $('.nav-tabs a[href="#' + tab + '"]').tab('show');
    };

    function displayfeatureeditor(mylayer, layerType) {
        //mylayer.closeTooltip();
        unsetHighlight(mylayer);
        var d = document.getElementById('Selected');
        fWidth = $('#Selected').width();
        fHeight = $('#Selected').height();
        XOff = 12;
        YOff = 25;
        var latlngCenter = getCenterofShape(mylayer);
        var centerPos = map.latLngToContainerPoint(latlngCenter);
        var panx = 0;
        var pany = 0;
        if (centerPos.x <= fWidth / 2) {
            if (centerPos.x < 0) {
                panx = centerPos.x + (map.getSize().x / 2) * -1;
                centerPos.x = map.getSize().x / 2;
            } else {
                panx = -fWidth / 2;
                centerPos.x = centerPos.x + (fWidth / 2);
            }
        } else if (centerPos.x >= map.getSize().x - fWidth / 2) {
            panx = fWidth / 2;
            if (centerPos.x > map.getSize().x) {
                panx = centerPos.x - map.getSize().x / 2;
                centerPos.x = map.getSize().x / 2;
            } else {
                panx = fWidth / 2;
                centerPos.x = centerPos.x - fWidth / 2;
            }
        }
        if (centerPos.y < 0) {
            pany = centerPos.y - map.getSize().y / 2;
            centerPos.y = map.getSize().y / 2;
        } else if (centerPos.y > map.getSize().y) {
            pany = centerPos.y - (map.getSize().y / 2);
            centerPos.y = map.getSize().y / 2;
        }
        if (centerPos.y + fHeight > map.getSize().y && centerPos.y + fHeight > map.getSize().y / 2) {
            pany = pany - (map.getSize().y - (centerPos.y + fHeight));
            pany = pany + 50;
            centerPos.y = centerPos.y + (map.getSize().y - (centerPos.y + fHeight));
            centerPos.y = centerPos.y - 50;
        }
        map.panBy([panx, pany]);
        //    map.panBy([0, pany]);
        centerPos.x = centerPos.x - XOff;
        if (centerPos.y > ((map.getSize().y) / 2)) {
            centerPos.y = centerPos.y - YOff;
            d.style.left = centerPos.x - fWidth / 2 + 'px';
            d.style.top = ((centerPos.y - fHeight) + 57) + 'px';
            $('#Selected').addClass('no-pseudo-before');
            $('#Selected').removeClass('no-pseudo-after');
        } else {
            d.style.left = centerPos.x - fWidth / 2 + 'px';
            d.style.top = (centerPos.y + 57) + 'px';
            $('#Selected').addClass('no-pseudo-after');
            $('#Selected').removeClass('no-pseudo-before');
        }
        var selectedPreset = $("#selectPresets").val();
        /*		if (selectedPreset != 'none') {
        			$("#rowAddPreset").hide();
        			$("#rowsubAddPreset").hide();
        		} else {
        			$("#rowAddPreset").show();
        			$("#rowsubAddPreset").show();
        		} */
        $("#rowAddPreset").show();
        $("#rowsubAddPreset").show();
        $('#Selected').css("display", "block");
    }
</script>
<div class="loader" id="loading" style="display:none;  position: absolute;top: calc(50% - 60px); left: calc(50% - 60px); z-index:3000">
</div>
<style>
    .loader {
            border: 16px solid #f3f3f3;
            /* Light grey */
            border-top: 16px solid #3498db;
            /* Blue */
            border-radius: 50%;
            width: 120px;
            height: 120px;
            animation: spin 2s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        @page {
            size: 8.5" 11";
            margin: 0;
        }

        .page {
            position: relative;
            width: 8.5in;
            height: 11in;
            margin: 0;
            border: none;
            page-break-after: always;
            -webkit-region-break-inside: avoid;
        }
    </style>

<script type="text/javascript" class="extract-script">
    function deleteSelectedFile() {
        currentFileID = layerid;
        var result = confirm("Are you sure you want to delete " + currentFileName + "?");
        if (result) {
            $.ajax({
                url: '/vpics/deletemaplayername',
                type: 'post',
                data: {
                    accessCode: vid,
                    layerid: currentFileID,
                },
                success: function (data) {
                    $.snackbar({
                        content: "File Deleted!"
                    });
                }
            });
            $("#layerlist").find("#" + currentFileID).remove();
            $("#filedeletebuttonopen").hide();
            $("#filedeletebuttonsave").hide();
            $("#file-open-name").val('');
            $("#file-name").val('');
        }
    }

    function getSavedMapLayers(mode = 'open') {
        opensavemode = mode;
        //$( "#table_JSON_debug" ).show();
        $("#help").hide();
        if (mode == 'open') {
            $("#fileOpenTable").show();
            $("#fileSaveTable").hide();
            $("#fileOpenSaveHeader").html('File Open');
            var func = 'openMapLayer';
            var func = 'fillSaveAsName';
        } else {
            $("#fileOpenTable").hide();
            $("#fileSaveTable").show();
            $("#fileOpenSaveHeader").html('File Save As');
            var func = 'fillSaveAsName';
        }
        $.ajax({
            url: '/vpics/getsavedmaplayers',
            type: 'post',
            data: {
                accessCode: vid
            },
            success: function (data) {
                var result = JSON.parse(data);
                savedMapLayers = result;
                // make it pretty for display in raw output
                var jsonPretty = JSON.stringify(result, null, 2);
                // populate the raw data
                $("#result").html(jsonPretty);
                var selectedBldg = $("#selectBuildings").val();
                $("#layerlist").empty();
                $.each(result, function (key, val) {
                    if ((selectedBldg == val.BuildingID) || (selectedBldg ==
                            '0')) {
                        var $li = $("<li class='unselectable' id='" + val.LayerID +
                            "' data-id='" + val.LayerID +
                            "' name='" +
                            val.LayerName + "'    ><a id='filelistname' onclick='" + func +
                            "(" + val
                            .LayerID +
                            ")'>" + val.LayerName + "</a></li>");
                        $("#layerlist").append($li);
                    }
                });
                $('#file-open-name').keyup(function () {
                    var valThis = $(this).val();
                    $("#filedeletebuttonopen").hide();
                    $("#filedeletebuttonsave").hide();
                    $('#layerlist>li').each(function () {
                        var text = $(this).text().toLowerCase();
                        (text.indexOf(valThis) == 0) ? $(this).show(): $(this).hide();
                    });
                });
                $('#file-name').keyup(function () {
                    $("#filedeletebuttonopen").hide();
                    $("#filedeletebuttonsave").hide();
                });
                activaTab('home');

            }
        });
    }
</script>
<script type="text/javascript" class="extract-script">
    function clearsummarytable() {
        $("#tbodyid tr").remove();
        $("#shapestbodyid tr").remove();
        $("#markerstbodyid tr").remove();
        $("#photostbodyid tr").remove();
    }
    $("#cb_showlabels").bind('change', function () {
        if (this.checked) {
            showlabel = 1;
        } else {
            showlabel = 0;
            drawnItemLabels.clearLayers();
        }
        saveLocal();
    })
    $(document).on('click', '.cb_showlegend', function () {
        //populateLegendTable();
        $('input[type="checkbox"].cb_showlegend:checked').each(function () {
            var rowColor = $(this).attr('data-color');
            for (var j = 0; j < tempLegendArray.length; j++) {
                if (tempLegendArray[j][0] == rowColor)
                    tempLegendArray[j][2] = 'checked';
            }
        });
        $('input[type="checkbox"].cb_showlegend:not(:checked)').each(function () {
            var rowColor = $(this).attr('data-color');
            for (var j = 0; j < tempLegendArray.length; j++) {
                if (tempLegendArray[j][0] == rowColor)
                    tempLegendArray[j][2] = '';
            }
        });
        //areanychecked();
    });

    function areanychecked() {
        if (typeof labelLegend !== "undefined")
            map.removeLayer(labelLegend);
        var bounds = map.getBounds();
        var min = bounds.getSouthWest();
        var max = bounds.getNorthEast();
        //alert(min.lat+' '+max.lng);
        var innerLegend = '';
        var len = $(".cb_showlegend:checked").length;
        if (len > 0) {
            //$( "#legend" ).show();
            innerLegend =
                '<div style="white-space:nowrap; cursor:pointer; z-index: 1000; min-width:120px;  overflow: none; position: absolute; bottom: 5px; right: 1px; font-family: Arial, sans-serif;    background-color: #fff!important; padding: 10px; margin: 10px; border: 1px solid #000; border-radius: 10px 10px 10px 10px; -moz-border-radius: 10px 10px 10px 10px; -webkit-border-radius: 10px 10px 10px 10px; ">';
            $('input[type="checkbox"].cb_showlegend:checked').each(function () {
                var rowColor = $(this).attr('data-color');
                var rowText = $(this).attr('data-legend');
                if (rowColor.length > 8) {
                    innerLegend = innerLegend +
                        "<div  style='margin-bottom:10px; width:150px;'><span style='display:block; float:left;' ><img src='" +
                        rowColor +
                        "' style='height:32px; width:auto;'></span> <span style='' >&nbsp;&nbsp;" +
                        rowText +
                        "</span><br></div>";
                } else {
                    innerLegend = innerLegend +
                        "<div  style='margin-bottom:10px; width:150px;'><span class='csssquare' style='display:block; float:left; background-color:" +
                        rowColor + ";'></span> <span style='' >&nbsp;&nbsp;" + rowText +
                        "</span><br></div>";
                }
            });
            innerLegend = innerLegend + '</div>';
            var label = new L.Marker([min.lat, max.lng], {
                icon: new L.DivIcon({
                    className: 'my-div-icon',
                    html: innerLegend
                }),
                draggable: 'true'
            });
            labelLegend = label.addTo(map);
        } else {
            $("#legend").hide();
        }
    }

    function displayLegend() {
        if (typeof labelLegend !== "undefined") {
            map.removeControl(labelLegend);
        }
        var bounds = map.getBounds();
        var min = bounds.getSouthWest();
        var max = bounds.getNorthEast();
        //alert(min.lat+' '+max.lng);
        var innerLegend = '';
        var displayblock = 0;
        var len = tempLegendArray.length;
        if (len > 0) {
            innerLegend =
                '<div style="white-space:nowrap; cursor:pointer; z-index: 1000; min-width:120px;  overflow: none; position: absolute; bottom: 5px; right: 1px; font-family: Arial, sans-serif;    background-color: #fff!important; padding: 10px; margin: 10px; border: 1px solid #000; border-radius: 10px 10px 10px 10px; -moz-border-radius: 10px 10px 10px 10px; -webkit-border-radius: 10px 10px 10px 10px; ">';
            for (var i = 0; i < len; i++) {
                var rowColor = tempLegendArray[i][0];
                var rowText = tempLegendArray[i][1];
                var match = 0;
                for (var j = 0; j < legendColorArray.length; j++) {
                    if (tempLegendArray[i][1] == legendColorArray[j][1]) {
                        match = 1;
                    }
                }
                if (match == 0)
                    continue;
                if (rowText == "none" || rowText == "None" || rowColor == null)
                    continue;
                displayblock = 1;
                if (rowColor.length > 8) {
                    innerLegend = innerLegend +
                        "<div  style='margin-bottom:10px; width:150px;'><span style='display:block; float:left;' ><img src='" +
                        rowColor +
                        "' style='height:32px; width:auto;'></span> <span style='' >&nbsp;&nbsp;" +
                        rowText + "</span><br></div>";
                } else {
                    innerLegend = innerLegend +
                        "<div  style='margin-bottom:10px; width:150px;'><span class='csssquare' style='display:block; float:left; background-color:" +
                        rowColor + ";'></span> <span style='' >&nbsp;&nbsp;" + rowText +
                        "</span><br></div>";
                }
            }
            innerLegend = innerLegend + '</div>';
            if (displayblock == 1) {
                label = L.control({
                    position: 'bottomright'
                });
                label.onAdd = function (map) {
                    var div = L.DomUtil.create('div', 'info legend'),
                        grades = [0, 10, 20, 50, 100, 200, 500, 1000],
                        labels = [];
                    div.innerHTML += innerLegend;
                    return div;
                };
                //legend.addTo(map);
                /*   var label = new L.Marker([min.lat, max.lng], {
                       icon: new L.DivIcon({
                           className: 'my-div-icon',
                           html: innerLegend
                       }),
                       draggable: 'false'
                   }); */
                labelLegend = label.addTo(map);
            }
        }
    }

    function addLabel(text = 'Label', location, whichfeature) {
        //TEXT LABELS
        var currentZoom = map.getZoom() - 6;
        var label = new L.Marker(location, {
            icon: new L.DivIcon({
                className: 'my-div-icon',
                html: '<div class="allLabels" style="white-space:nowrap; cursor:pointer; z-index: 1000;  overflow: none; position: absolute; bottom: 5px; right: 1px; font-family: Arial, sans-serif; font-size:' +
                    currentZoom +
                    '; font-weight:bold; color:red; text-shadow: 2px 2px 2px #FFffff; ">' +
                    text + '</div>'
            }),
            draggable: 'false'
        });
        var newmarker = label.addTo(drawnItemLabels);
    }

    function showHelp() {
        $("#table_JSON_debug").hide();
        $("#help").show();
    }

    function clearSelected() {
        $("#rowTextArea").hide();
        $("#rowTextSize").hide();
        $("#rowPhoto").hide();
        $("#rowPhotoDate").hide();
        $("#rowOpacity").hide();
        $("#rowHole").hide();
        $("#rowColor").hide();
        $("#rowFillColor").hide();
        $("#rowArrow").hide();
        $("#rowAmimatedIcon").hide();
        $("#rowName").hide();
        $("#rowPresets").hide();
        $("#rowsubPresets").hide();
        $("#rowIconNumber").hide();
        $("#rowIcon").hide();
        $("#rowDescription").hide();
        $("#rowArea").hide();
        $("#rowPerimeter").hide();
        $("#rowClone").hide();
        $("#rowAddPreset").hide();
        $("#rowsubAddPreset").hide();
    }

    function clearlegendTable() {
        $("#legendtbodyid tr").remove();
        legendColorArray.splice(0, legendColorArray.length);
        tempLegendArray.splice(0, tempLegendArray.length);
    }

    function populateLegendTable() {
        $("#legendtbodyid tr").remove();
        for (var i = 0; i < legendColorArray.length; i++) {
            var description = '';
            var isChecked = '';
            for (var j = 0; j < tempLegendArray.length; j++) {
                if (tempLegendArray[j][0] == legendColorArray[i][0]) {
                    //legendColorArray.push([feature.properties.color,feature.properties.preset])
                    if (typeof tempLegendArray[j][1] !== "undefined")
                        description = tempLegendArray[j][1];
                    else
                        description = '';
                    isChecked = tempLegendArray[j][2];
                }
            }
            //if it does NOT find a match, add a new row to the temp array
            if ((description.length == 0) && (isChecked.length == 0)) {
                tempLegendArray.push([legendColorArray[i][0], legendColorArray[i][1], '0'])
                description = legendColorArray[i][1];
                isChecked = '';
            }
            if (typeof description == "undefined") {
                description = '';
            }
            //IF IT IS A MARKER, CREATE A DIFFERENT ROW THAN IF IT IS A SHAPE
            if (legendColorArray[i][0] != null) {
                if (legendColorArray[i][0].length > 8) {
                    //ADD ROW TO THE LEGEND TABLE IN THE SUMMARY TAB
                    $("#legendtbodyid").newAppend("<tr><td style='padding-left:10px;'><img src='" +
                        legendColorArray[i]
                        [0] +
                        "' style='height:32px; width:auto;'></td><td  style='padding:5px;' id=''>" +
                        description + "</td><td style='padding:5px;'><input type='checkbox' " +
                        isChecked +
                        "  class='cb_showlegend' data-legend='" + legendColorArray[i][1] +
                        "'  data-color='" +
                        legendColorArray[i][0] + "'  id='cb_showlegenditem_" + i + "' ></td></tr>");
                } else {
                    //ADD ROW TO THE LEGEND TABLE IN THE SUMMARY TAB
                    $("#legendtbodyid").newAppend(
                        "<tr><td style='padding-left:10px;'><span class='csssquare' style='display:block; background-color:" +
                        legendColorArray[i][0] + ";'></span></td><td  style='padding:5px;' id=''>" +
                        description +
                        "</td><td style='padding:5px;'><input type='checkbox' " + isChecked +
                        "  class='cb_showlegend' data-legend='" + legendColorArray[i][1] +
                        "'  data-color='" +
                        legendColorArray[i][0] + "'  id='cb_showlegenditem_" + i + "' ></td></tr>");
                }
            }
            //ADD ROW TO THE LEGEND TABLE IN THE SUMMARY TAB
            //	$( "#legendtbodyid" ).append( "<tr><td style='padding-left:10px;'><span class='csssquare' style='display:block; background-color:"+legendColorArray[i][0]+";'></span></td><td contenteditable style='padding:5px;'>"+legendColorArray[i][1]+"</td><td style='padding:5px;'><input type='checkbox' class='cb_showlegend' data-legend='"+legendColorArray[i][1]+"'  data-color='"+legendColorArray[i][0]+"'  id='cb_showlegenditem_"+i+"' ></td></tr>" );
        }
    }

    function layerFilterLegend(clickobject, filterLayer) {
        var hideAll;
        var atleatOne;
        var hideLayer;
        jqclickObject = $(clickobject);
        if (filterLayer == "all") {
            if ($(jqclickObject).hasClass("activated")) {
                hideAll = true;
                $.snackbar({
                    content: "All groups are now hidden from Legend."
                });
            } else {
                hideAll = false;
                $.snackbar({
                    content: "All groups labels are now visible in Legend."
                });
            }
            $(".legendClass").each(function () {
                if (hideAll == true) {
                    $(this).removeClass("activated");
                    $(this).addClass("deactivated");
                    drawnItems.eachLayer(function (layer) {
                        layer.properties.layerlegend = "false";
                    });
                    filteredItems.eachLayer(function (layer) {
                        layer.properties.layerlegend = "false";
                    });
                } else {
                    $(this).removeClass("deactivated");
                    $(this).addClass("activated");
                    drawnItems.eachLayer(function (layer) {
                        layer.properties.layerlegend = "true";
                    });
                    filteredItems.eachLayer(function (layer) {
                        layer.properties.layerlegend = "true";
                    });
                }
            });
        } else {
            if ($(jqclickObject).hasClass("activated")) {
                drawnItems.eachLayer(function (layer) {
                    if (layer.properties.preset == filterLayer) {
                        if ($(jqclickObject).hasClass("activated")) {
                            $(jqclickObject).removeClass("activated");
                            $(jqclickObject).addClass("deactivated");
                            $.snackbar({
                                content: filterLayer +
                                    " group legend is now hidden."
                            });
                        }
                        layer.properties.layerlegend = "false";
                    }
                });
                filteredItems.eachLayer(function (flayer) {
                    if (flayer.properties.preset == filterLayer) {
                        if ($(jqclickObject).hasClass("activated")) {
                            $(jqclickObject).removeClass("activated");
                            $(jqclickObject).addClass("deactivated");
                            $.snackbar({
                                content: filterLayer +
                                    " group legend is now hidden."
                            });
                        }
                        flayer.properties.layerlegend = "false";
                    }
                });
            } else {
                drawnItems.eachLayer(function (layer) {
                    if (layer.properties.preset == filterLayer) {
                        if ($(jqclickObject).hasClass("deactivated")) {
                            $(jqclickObject).removeClass("deactivated");
                            $(jqclickObject).addClass("activated");
                            $.snackbar({
                                content: filterLayer +
                                    " group legend is now visible"
                            });
                        }
                        layer.properties.layerlegend = "true";
                    }
                });
                filteredItems.eachLayer(function (flayer) {
                    if (flayer.properties.preset == filterLayer) {
                        if ($(jqclickObject).hasClass("deactivated")) {
                            $(jqclickObject).removeClass("deactivated");
                            $(jqclickObject).addClass("activated");
                            $.snackbar({
                                content: filterLayer +
                                    " group legend is now visible"
                            });
                        }
                        flayer.properties.layerlegend = "true";
                    }
                });
            }
        }
        saveLocal();
        displayLegend();
    }

    function layerFilterLabels(clickobject, filterLayer) {
        var hideAll;
        var atleatOne;
        var hideLayer;
        jqclickObject = $(clickobject);
        if (filterLayer == "all") {
            if ($(jqclickObject).hasClass("activated")) {
                hideAll = true;
                $.snackbar({
                    content: "All groups labels are now hidden."
                });
            } else {
                hideAll = false;
                $.snackbar({
                    content: "All groups labels are now visible."
                });
            }
            $(".labelClass").each(function () {
                if (hideAll == true) {
                    $(this).removeClass("activated");
                    $(this).addClass("deactivated");
                    drawnItems.eachLayer(function (layer) {
                        layer.properties.labelvisible = "false";
                    });
                    filteredItems.eachLayer(function (layer) {
                        layer.properties.labelvisible = "false";
                    });
                } else {
                    $(this).removeClass("deactivated");
                    $(this).addClass("activated");
                    drawnItems.eachLayer(function (layer) {
                        layer.properties.labelvisible = "true";
                    });
                    filteredItems.eachLayer(function (layer) {
                        layer.properties.labelvisible = "true";
                    });
                }
            });
        } else {
            if ($(jqclickObject).hasClass("activated")) {
                drawnItems.eachLayer(function (layer) {
                    if (layer.properties.preset == filterLayer) {
                        if ($(jqclickObject).hasClass("activated")) {
                            $(jqclickObject).removeClass("activated");
                            $(jqclickObject).addClass("deactivated");
                            $.snackbar({
                                content: filterLayer +
                                    " group labels are now hidden"
                            });
                        }
                        layer.properties.labelvisible = "false";
                    }
                });
                filteredItems.eachLayer(function (flayer) {
                    if (flayer.properties.preset == filterLayer) {
                        if ($(jqclickObject).hasClass("activated")) {
                            $(jqclickObject).removeClass("activated");
                            $(jqclickObject).addClass("deactivated");
                            $.snackbar({
                                content: filterLayer +
                                    " group labels are now hidden."
                            });
                        }
                        flayer.properties.labelvisible = "false";
                    }
                });
            } else {
                drawnItems.eachLayer(function (layer) {
                    if (layer.properties.preset == filterLayer) {
                        if ($(jqclickObject).hasClass("deactivated")) {
                            $(jqclickObject).removeClass("deactivated");
                            $(jqclickObject).addClass("activated");
                            $.snackbar({
                                content: filterLayer +
                                    " group labels are now visible"
                            });
                        }
                        layer.properties.labelvisible = "true";
                    }
                });
                filteredItems.eachLayer(function (flayer) {
                    if (flayer.properties.preset == filterLayer) {
                        if ($(jqclickObject).hasClass("deactivated")) {
                            $(jqclickObject).removeClass("deactivated");
                            $(jqclickObject).addClass("activated");
                            $.snackbar({
                                content: filterLayer +
                                    " group labels are now visible"
                            });
                        }
                        flayer.properties.labelvisible = "true";
                    }
                });
            }
        }
        saveLocal();
    }

    function filterSummaryTab(clickobject, filterLayer) {
        var hideAll;
        var atleatOne;
        var hideLayer;
        jqclickObject = $(clickobject);
        if (filterLayer == "all") {
            if ($(jqclickObject).hasClass("activated")) {
                hideAll = true;
                $.snackbar({
                    content: "All groups are now hidden."
                });
            } else {
                hideAll = false;
                $.snackbar({
                    content: "All groups are now visible."
                });
            }
            $(".filterClass").each(function () {
                if (hideAll == true) {
                    $(this).removeClass("activated");
                    $(this).addClass("deactivated");
                    drawnItems.eachLayer(function (layer) {
                        layer.properties.layervisible = "false";
                        if (layer.properties.anim == "true")
                            addPolylineAnimation(layer, false);
                        filteredItems.addLayer(layer);
                        drawnItems.removeLayer(layer);
                    });
                } else {
                    $(this).removeClass("deactivated");
                    $(this).addClass("activated");
                    drawnItems.eachLayer(function (layer) {
                        layer.properties.layervisible = "true";
                    });
                    filteredItems.eachLayer(function (layer) {
                        layer.properties.layervisible = "true";
                        filteredItems.removeLayer(layer);
                        drawnItems.addLayer(layer);
                        if (layer.properties.anim == "true")
                            addPolylineAnimation(layer, true);
                    });
                }
            });
            //saveLocal();
        } else {
            if ($(jqclickObject).hasClass("activated")) {
                drawnItems.eachLayer(function (layer) {
                    if (layer.properties.preset == filterLayer) {
                        if ($(jqclickObject).hasClass("activated")) {
                            $(jqclickObject).removeClass("activated");
                            $(jqclickObject).addClass("deactivated");
                            $.snackbar({
                                content: filterLayer + " group is now hidden"
                            });
                        }
                        layer.properties.layervisible = "false";
                        if (layer.properties.anim == "true")
                            addPolylineAnimation(layer, false);
                        drawnItems.removeLayer(layer);
                        filteredItems.addLayer(layer);
                        //saveLocal();
                    }
                });
            } else {
                filteredItems.eachLayer(function (flayer) {
                    if (flayer.properties.preset == filterLayer) {
                        if ($(jqclickObject).hasClass("deactivated")) {
                            $(jqclickObject).removeClass("deactivated");
                            $(jqclickObject).addClass("activated");
                            $.snackbar({
                                content: filterLayer + " group is now visible"
                            });
                        }
                        flayer.properties.layervisible = "true";
                        drawnItems.addLayer(flayer);
                        filteredItems.removeLayer(flayer);
                        if (flayer.properties.anim == "true")
                            addPolylineAnimation(flayer, true);
                        //saveLocal();
                    }
                });
            }
        }
        //			updateLayers();
        saveLocal();
    }

    function calculateAreaTotals() {
        var shapearea = 0;
        var shapeperim = 0;
        $("#shapestbodyid").find("tr").each(function () { //get all rows in table
            var areaInCell = $(this).find('td.Area').text();
            if (areaInCell.length > 0) {
                areaInCell = areaInCell.substr(0, (areaInCell.length - 5));
                shapearea += parseInt(areaInCell.replace(/,/g, ''), 10);
            }
            var perimInCell = $(this).find('td.perim').text();
            if (perimInCell.length > 0) {
                perimInCell = perimInCell.substr(0, (perimInCell.length - 3));
                shapeperim += parseInt(perimInCell.replace(/,/g, ''), 10);
            }
        });
        $("#rowTotalArea").show();
        $("#rowTotalPerim").show();
        shapearea = numeral(Math.round(shapearea)).format('0,0');
        shapearea = shapearea + ' sqft';
        //           $("#totalArea").html(shapearea);
        shapeperim = numeral(Math.round(shapeperim)).format('0,0');
        shapeperim = shapeperim + ' ft';
        //           $("#totalPerim").html(shapeperim);
    }

    function selectItemManually(selectedLayerId) {
        drawnItems.eachLayer(function (layer) {
            if (layer._leaflet_id == selectedLayerId)
                layer.fireEvent('click');
        });
    }

    var a;

     function toggleControls(show) {
            let hideClasses = [
                "leaflet-gac-search-icon",
            "leaflet-gac-wrapper",
             "my-fab-button",
             "leaflet-control-zoom",
             "panel-collapse",
             "leaflet-left",
             "leaflet-draw-toolbar",
             "leaflet-pm-toolbar",
             "leaflet-text-control",
             "leaflet-control-layers"
            ]
            for (let i = 0; i < hideClasses.length; i++) {

                if (document.getElementsByClassName(hideClasses[i])[0] === undefined)
                    continue;
                var controlContainer = document.getElementsByClassName(hideClasses[i])[0];
                if (show) {
                    controlContainer.style.display = 'block';
                    //var k = document.getElementsByClassName('leaflet-legend-control');
                    //k[0].style.top = "0px";
                } else {
                    controlContainer.style.display = 'none';

                   // var k = document.getElementsByClassName('leaflet-legend-control');
                   // k[0].style.top = "-50px";
                }
            }
        }
    async function exportbase64() {
            this.toggleControls(false);
            let orignalBounds = map.getBounds();
            //map.fitBounds(zoomBounds);
           // this.enlargePhotoMarkers();
           return new Promise(resolve => setTimeout(function () {
                var node = document.getElementById('map');
                var controlContainer = document.getElementsByClassName("leaflet-left")[0];
               // let logourl = this.clogo;
                let pagecounter = 1;
                let j = 0;
                controlContainer.style.display = 'none';
                domtoimage.toPng(node, {
                    height: 350,
                    width: 350,
                    cacheBust: true,
                }).then(function (mydataUrl) {

                    //this.resetPhotoMarkers();
                    var controlContainer = document.getElementsByClassName("leaflet-control-container")[0];
                    controlContainer.style.display = 'block';
                    this.toggleControls(true);
                    resolve(mydataUrl);
                }.bind(this))
                    .catch(function (error) {

                        var controlContainer = document.getElementsByClassName("leaflet-control-container")[0];
                        map.fitBounds(orignalBounds);
                        //this.resetPhotoMarkers();
                        controlContainer.style.display = 'block';
                        console.error('oops, something went wrong!', error);
                        this.toggleControls(true);
                        reject();
                    }.bind(this));
            }.bind(this), 700));


        }

      var exportData;

    function copytable() {

        var k = [];
        var x = 0;
        $("#shapesExportTable").empty(); //Added to fix the bug in export to excel where old data was already in table.
        $("#shapesExportTable").append("<thead><tr><td>" + "Group" + "</td><td>" + "Name" +
            "</td><td>" +
            "Area" +
            "</td><td>" + "Perimeter" + "</td></tr></thead><tbody id='shapesExporttbodyid'>");
        /*	$("#shapestbodyid").find("tr").each(function () { //get all rows in table
        		//$(this).find('td.colorcolumn').remove();
        		k[x] = {};
        		k[x].colName = $(this).find('td.featureName').text();
        		if ($(this).find('td.featureGroup').text() != "undefined") {
        			k[x].colGroup = presets[$(this).find('td.featureGroup').text()].name;
        		} else {
        			k[x].colGroup = 'UnGrouped';
        		}
        		k[x].colArea = parseInt($(this).find('td.Area').text().replace(/\D/g, ''));
        		k[x].colPerim = parseInt($(this).find('td.perim').text().replace(/\D/g, ''));
        		if (isNaN(k[x].colArea))
        			k[x].colArea = 0;
        		//
        		x++;
        		//ADD ROW TO THE SHAPES TABLE IN THE SUMMARY TAB
        	}); */
        for (var preset in presets) {
            if (presets[preset].vectorgroup) {
                for (var glayer in presets[preset].leafletlayer._layers) {

                    k[x] = {};
                    k[x].colName = presets[preset].leafletlayer._layers[glayer].feature.properties.name;
                    if (preset == 'none') {
                        k[x].colGroup = "Ungrouped";
                    } else {
                        k[x].colGroup = presets[preset].name;
                    }
                    k[x].colArea = parseInt(presets[preset].leafletlayer._layers[glayer].feature.properties.area.replace(
                        /\D/g, ''));
                    k[x].colPerim = parseInt(presets[preset].leafletlayer._layers[glayer].feature.properties.perimeter
                        .replace(/\D/g,
                            ''));
                    if (isNaN(k[x].colArea))
                        k[x].colArea = 0;
                    //
                    x++;
                }
            }
        }
        var result = groupBy(k, function (item) {
            return [item.colGroup];
        });
        var grandcolPerim = 0;
        var grandcolArea = 0;
        exportData = result;
        generateBlob();
        $("#shapesExportTable").hide();
    }

    function groupBy(array, f) {
        var groups = {};
        array.forEach(function (o) {
            var group = JSON.stringify(f(o));
            groups[group] = groups[group] || [];
            groups[group].push(o);
        });
        return Object.keys(groups).map(function (group) {
            return groups[group];
        })
    }


    $("#selectBuildings").change(function () {
        if (opensavemode == 'open') {
            $("#fileOpenSaveHeader").html('File Open');
            $("#fileOpenTable").show();
            $("#fileSaveTable").hide();
            var func = 'openMapLayer';
            var func = 'fillSaveAsName';
        } else {
            $("#fileOpenTable").hide();
            $("#fileSaveTable").show();
            $("#fileOpenSaveHeader").html('File Save As');
            var func = 'fillSaveAsName';
        }
        var selectedBldg = $("#selectBuildings").val();
        $("#layerlist").empty();
        $.each(savedMapLayers, function (key, val) {
            if ((selectedBldg == val.BuildingID) || (selectedBldg == '0')) {
                var $li = $("<li class='unselectable' id='" + val.LayerID +
                    "'  name='" +
                    val.LayerName +
                    "'    ><a onclick='" + func + "(" + val.LayerID + ")'>" +
                    val.LayerName +
                    "</a></li>");
                $("#layerlist").append($li);
            }
        });
    });
    var gmap;
    var gsmap;
    var bsmap;
    var bbmap;
    var myCenter;
    var loaded;
    var loadedtype;
    var loadedvar;
    var disableeventlistner = false;
 
    $('.leaflet-control-attribution').hide()
</script>


<style>
         #filesearch {
            background-image: url('/images/searchicon.png');
            background-position: 10px 12px;
            background-repeat: no-repeat;
            width: 100%;
            font-size: 16px;
            padding: 12px 20px 12px 40px;
            border: 1px solid #ddd;
            margin-bottom: 12px;
        }

        #filelist {
            max-height: 200px;
            overflow-y: auto;
        }

        .my-fab-button {
            background: #ff6600 !important;
            width: 40px !important;
            height: 40px !important;
        }

        .my-fab-button:hover {
            opacity: 0.8;
        }

        .my-fab-icon {
            background: #ff6600 !important;
            color: white;
        }
    </style>

<script src="/js/mapmaker/popper.min.js"></script>
<script src="/js/mapmaker/bootstrap.min.js"></script>
<link rel="stylesheet" href="/css/mapmaker/jquery-confirm.min.css">
<script src="/js/mapmaker/jquery-confirm.min.js"></script>
<link rel="stylesheet" href="/css/mapmaker/snackbar.min.css">
<link rel="stylesheet" href="/css/mapmaker/material.css">
<script src="/js/mapmaker/jquery.ui.touch-punch.min.js"></script>
<script src="/js/mapmaker/snackbar.min.js"></script>
<script src="/js/mapmaker/dom-to-image.min.js"></script>
<script src="/js/leaflet/leaflet.polylineDecorator.js"></script>
<script src="/js/mapmaker/intro.js"></script>
<div id="scriptcontainer2"></div>
<!--
	* Handle Bar Templates
	-->

<script id="group-template" type="text/x-handlebars-template">
    <div class="leaflet-group list-group-item" id="{{groupId}}-card" onclick="selectGroup('{{groupId}}')">
            <div class="title">
                <!--span class="colorband" style="background-color:{{fillColor}}">&nbsp;</span-->

                <span class="collapse-icons leaflet-group-name" data-toggle="collapse" data-target="#{{groupId}}-items" data-parent="{{groupId}}-card">{{groupName}}

                </span>
                <span class="caret"></span>




                {{#if groupArea}}
                <span class="group-area">{{groupArea}}</span>{{/if}} {{#if groupPerim}}
                <span class="group-perim">{{groupPerim}}</span>{{/if}}
                <!--span class="glyphicon glyphicon-triangle-bottom collapse-icons" data-toggle="collapse" data-target="#{{groupId}}-items" data-parent="{{groupId}}-card"
				    style="float:right"></span-->
                {{#if cogEnabled}}
                <span class="glyphicon glyphicon-cog collapse-icons" data-toggle="tooltip" title="Edit Properties" onclick="showGroupPropEditor('{{groupId}}')"
                    data-parent="{{groupId}}-card" style="float:right;padding-left:5px;padding-right:12px;font-size:16px;"></span>
                {{/if}}
            </div>
            <!--div class="subtitle">
				<span class="colorband">&nbsp;</span>
				{{#if groupArea}}
				<span class="group-area">{{groupArea}}</span>{{/if}} {{#if groupPerim}}
				<span class="group-perim">{{groupPerim}}</span>{{/if}}
			</div-->
            <ul id="{{groupId}}-items" class="list-group collapse group-items">
            </ul>
            <ul id="{{groupId}}-properties" class="list-group collapse group-properties">
            </ul>
        </div>
    </script>

<script id="layer-template" type="text/x-handlebars-template">
    <li class="leaflet-layer-item" id="{{layerId}}">
            <div class="layer-title" onclick="selectLayer('{{layerId}}','{{groupId}}')">
                {{#if polygon}}
                <i class="icon-polygon" aria-hidden="true"></i>&nbsp; {{/if}} {{#if circle}}
                <i class="fa fa-circle" aria-hidden="true"></i>&nbsp; {{/if}} {{#if line}}
                <i class="fa fa-arrows-h" aria-hidden="true"></i>&nbsp; {{/if}} {{#if rect}}
                <i class="fa fa-square" aria-hidden="true"></i>&nbsp; {{/if}} {{layerName}}

                <span class="layer-area">
                    &nbsp;&nbsp{{layerArea}}</span>&nbsp;&nbsp; &nbsp;&nbsp;
                <span class="layer-perim">{{layerPerim}}</span>
            </div>
            <!--div class="layer-subtitle">
				<span class="layer-area">
					<i class='fa fa-th'>&nbsp;&nbsp;</i>{{layerArea}}</span>&nbsp;&nbsp;
				<i class='fa fa-arrows-v'>&nbsp;&nbsp;</i>
				<span class="layer-perim">{{layerPerim}}</span>
			</div-->
        </li>
    </script>
<script id="group-properties-template" type="text/x-handlebars-template">
    <div class="n-container item-properties">

            <div class="n-row">
                <div class="n-col" id="propertiesGroupName" contenteditable="true">
                    {{groupName}}
                </div>
            </div>
            {{#if vector}}
            <div class="n-row">
                <div class="n-col _15 text-label">Line</div>
                <div class="n-col _10">
                    <select id="lineColorSelector" class="colorSelector" style="width:6em;">
                        {{#each colors}}
                        <option value='{{this}}' data-color='{{this}}'></option>
                        {{/each}}
                    </select>
                </div>

                <div class="n-col _35">
                    <select id="lineTypeSelector" class="form-control input-small lineTypeSelector" style="width:6em;">
                        <option value='solid'>Solid</option>
                        <option value='dotted'>Dotted</option>
                        <option value='dashed'>Dashed</option>
                    </select>
                </div>
                <div class="n-col _10"></div>
            </div>
            <div class="n-row">
                <div class="n-col _15 text-label">Fill</div>
                <div class="n-col _10">
                    <select id="fillColorSelector" class="colorSelector">
                        {{#each colors}}
                        <option value='{{this}}' data-color='{{this}}'></option>
                        {{/each}}
                    </select>
                </div>
                <div class="n-col _25">
                    <input class="form-control input-small fillOpacitySelector" type="number" min="0" max="1" step="0.1" value={{fillOpacity}}>
                </div>
                <div class="n-col _35"></div>
                <div class="n-col _10"></div>
            </div>
            <div class="n-row">
                <!--div class="n-col _15 text-label">Default</div>
				<div class="n-col _20">
					<label class="switch">
						<input type="checkbox">
						<span class="slider round"></span>
					</label>
				</div-->
                <div class="n-col _15 text-label">Hide</div>
                <div class="n-col _20">
                    <label class="switch">
                        <input class="hide-group" type="checkbox">
                        <span class="slider round"></span>
                    </label>
                </div>
            </div>

            {{/if}}
            <div class="n-row">
                <div style="margin: 0 auto;">
                    <button class="btn btn-primary" onclick="applyGroupProperties('{{groupId}}');">Save</button>
                </div>
                <!--div class="n-col _60"></div>
				<div class="n-col _20">
					<i class="fa fa-clone group-actions"></i>
					<i class="fa fa-trash group-actions"></i>
				</div-->
            </div> {{#if photo}} {{/if}} {{#if marker}} {{/if}} {{#if text}} {{/if}}
        </div>
    </script>


<script class="extract-script">

    function selectLayer(layerId, groupId) {

        var layer = presets[groupId].leafletlayer.getLayer(layerId);

        layer.fireEvent('click');
    }

 

    /**
     * Selects the group based on onclick event in the group list. Sets a global variable with the presetid of the selected group
     *
     * @param {string:presetid} groupId
     */
 
    function addGroupFromList() {
        selectedGroup = $('#group-list-select').val();
        selectedGroupShort = selectedGroup.toLowerCase();
        selectedGroupShort = selectedGroupShort.replace(/\s+/g, '');
        groupdata = calcAreaPerimTotals(presets[selectedGroupShort].leafletlayer);
        if (groupdata.items == 0 && $('#' + selectedGroupShort + '-card').length == 0) {
            var context = {
                groupName: presets[selectedGroupShort].name,
                groupId: selectedGroupShort,
                fillColor: presets[selectedGroupShort].fillColor,
                color: presets[selectedGroupShort].color
            };
            var html = groupTemplate(context);

        }
        selectGroup(selectedGroupShort);
        $('#group-list-select').val("");
    }

    function findLayerGroup(layer) {

    }
    /**
     * Adds the layer to panel under its group.
     *
     * @param {string:presetid} groupId
     * @param {layer:leaflet-layer} layer
     */

    /**
     * Update layer properties, calculate area and perims based on changes made by the user
     *
     * @param {layer:leaflet-layer} layer
     */
    function updateLayerProps(layer) {
        if (layer instanceof L.Marker) {
            if (layer.feature.properties.type == 'Photo') {
                layer.feature.properties.type = 'Photo';
                layer.feature.properties.location = layer._latlng;
                layer.feature.properties.markerURL = layer.options.icon.options.iconUrl;
                layer.feature.properties.markerSize = layer.options.icon.options.iconSize;
            } else {
                layer.feature.properties.type = 'Marker';
                layer.feature.properties.location = layer._latlng;
                layer.feature.properties.markerSize = layer.options.icon.options.iconSize;
                layer.feature.properties.markerURL = layer.options.icon.options.iconUrl;
                if (layer.feature.properties.name == '')
                    layer.feature.properties.name = 'Marker-' + layer.feature.properties.id;
            }
        } else if (layer instanceof L.Circle) {
            var center = layer.getLatLng(),
                rad = layer.getRadius();
            radius = rad * 3.28084;
            area = radius * radius * Math.PI;
            distance = (radius + radius) * Math.PI;
            distance = numeral(_round(distance, 2)).format('0,0');
            if (layer.feature.properties.name == '')
                layer.feature.properties.name = 'Circle-' + layer.feature.properties.id;
            subtractThis = getSubtractAreas(layer.feature.properties.name);
            area = area - subtractThis;
            area = numeral(Math.round(area)).format('0,0');
            layer.feature.properties.type = 'Circle';
            layer.feature.properties.radius = rad;
            layer.feature.properties.color = layer.options.color;
            layer.feature.properties.fillColor = layer.options.fillColor;
            layer.feature.properties.fillOpacity = layer.options.fillOpacity;
            layer.feature.properties.stroke = layer.options.stroke;
            layer.feature.properties.strokeWeight = layer.options.weight;
            layer.feature.properties.strokeWeight = 1;
            layer.feature.properties.area = area + ' sqft';
            layer.feature.properties.perimeter = distance + ' ft';
            layer.on('mouseover', function (e) {
                setHighlight(layer);
                if ($('#Selected').is(":visible"))
                    unsetHighlight(layer);
            });
            layer.on('mouseout', function (e) {
                unsetHighlight(layer);
            });
        } else if (layer instanceof L.Rectangle) {
            //RECTANGLE
            layer.feature.properties.type = 'Rectangle';
            layer.feature.properties.color = layer.options.color;
            layer.feature.properties.fillColor = layer.options.fillColor;
            layer.feature.properties.fillOpacity = layer.options.fillOpacity;
            layer.feature.properties.stroke = layer.options.stroke;
          //  layer.feature.properties.strokeWeight = layer.options.weight;
            layer.feature.properties.strokeWeight = 1;
            var latlngs = layer._defaultShape ? layer._defaultShape() : layer.getLatLngs(),
                distance = 0,
                area = Math.round(L.GeometryUtil.geodesicArea(latlngs) *
                    10.76391041671);
            if (layer.feature.properties.name == '')
                layer.feature.properties.name = 'Rect-' + layer.feature.properties.id;
            var subtractThis = getSubtractAreas(layer.feature.properties.name);
            area = area - subtractThis;
            area = numeral(area).format('0,0');
            layer.feature.properties.area = area + ' sqft';
            if (latlngs.length > 2) {
                for (var i = 0; i < latlngs.length - 1; i++) {
                    distance += latlngs[i].distanceTo(latlngs[i + 1]);
                }
                distance += latlngs[latlngs.length - 1].distanceTo(latlngs[0]);
                distance = distance * 3.28084;
                distance = numeral(_round(distance, 2)).format('0,0');
            }
            layer.feature.properties.perimeter = distance + ' ft';
            layer.on('mouseover', function (e) {
                setHighlight(layer);
                if ($('#Selected').is(":visible"))
                    unsetHighlight(layer);
            });
            layer.on('mouseout', function (e) {
                unsetHighlight(layer);
            });
        } else if (layer instanceof L.Polygon) {
            layer.feature.properties.type = 'Polygon';
            layer.feature.properties.color = layer.options.color;
            layer.feature.properties.fillColor = layer.options.fillColor;
            layer.feature.properties.fillOpacity = layer.options.fillOpacity;
            layer.feature.properties.stroke = layer.options.stroke;
          //  layer.feature.properties.strokeWeight = layer.options.weight;
            layer.feature.properties.strokeWeight = 1;
            latlngs = layer._defaultShape ? layer._defaultShape() : layer.getLatLngs();
            distance = 0;
            area = Math.round(L.GeometryUtil.geodesicArea(latlngs) *
                10.76391041671);
            if (layer.feature.properties.name == '')
                layer.feature.properties.name = 'Poly-' + layer.feature.properties.id;
            subtractThis = getSubtractAreas(layer.feature.properties.name);
            area = area - subtractThis;
            area = numeral(area).format('0,0');
            layer.feature.properties.area = area + ' sqft';
            if (latlngs.length > 2) {
                for (i = 0; i < latlngs.length - 1; i++) {
                    distance += latlngs[i].distanceTo(latlngs[i + 1]);
                }
                distance += latlngs[latlngs.length - 1].distanceTo(latlngs[0]);
                distance = distance * 3.28084;
                distance = numeral(_round(distance, 2)).format('0,0');
            }
            layer.feature.properties.perimeter = distance + ' ft';
            layer.on('mouseover', function (e) {
                setHighlight(layer);
                if ($('#Selected').is(":visible"))
                    unsetHighlight(layer);
            });
            layer.on('mouseout', function (e) {
                unsetHighlight(layer);
            });
        } else if (layer instanceof L.Polyline) {
            layer.feature.properties.type = 'Line';
            layer.feature.properties.color = layer.options.color;
            layer.feature.properties.fillColor = layer.options.fillColor;
            layer.feature.properties.stroke = layer.options.stroke;
          //  layer.feature.properties.strokeWeight = layer.options.weight;
            layer.feature.properties.strokeWeight = 1;
           latlngs = layer._defaultShape ? layer._defaultShape() : layer.getLatLngs();
            distance = 0;
            if (latlngs.length < 2) {
                return "Distance: N/A";
            } else {
                for (i = 0; i < latlngs.length - 1; i++) {
                    distance += latlngs[i].distanceTo(latlngs[i + 1]);
                }
                distance = distance * 3.28084;
                distance = numeral(_round(distance, 2)).format('0,0');
            }
            layer.feature.properties.perimeter = distance + ' ft';
            if (layer.feature.properties.name == '')
                layer.feature.properties.name = 'Line-' + layer.feature.properties.id;
            layer.on('mouseover', function (e) {
                // Set highlight
                setHighlight(layer);
                if ($('#Selected').is(":visible"))
                    unsetHighlight(layer);
            });
            layer.on('mouseout', function (e) {
                unsetHighlight(layer);
            });
        }
    }
    /**
     * Calculates the Area and Perimeter of all the layers (featuregroup/layergroup/layer provided as an
     * argument. Calculation is based on the properties of the layers in featuregroup/layergroup.
     * @param {L.FeatureGroup} layers - layers for which perims and area need to be calculated.
     * @returns {object} containing perim and area of all the layers.
     */
    function calcAreaPerimTotals(layers) {
        var myArea = 0,
            myPerim = 0,
            myItems = 0;
        if (layers._layers !== undefined) {
            for (var key in layers._layers) {
                if (layers._layers.hasOwnProperty(key)) {
                    if (layers._layers[key]._layers !== undefined) {
                        for (var subkey in layers._layers[key]._layers) {
                            if (layers._layers[key]._layers.hasOwnProperty(subkey)) {
                                if (layers._layers[key]._layers[subkey].feature.properties !== undefined) {
                                    if (layers._layers[key]._layers[subkey].feature.properties.area !== undefined)
                                        myArea = myArea + parseFloat(layers._layers[key]._layers[subkey].feature.properties
                                            .area.replace(/[^0-9]/g,
                                                ''))
                                    if (layers._layers[key]._layers[subkey].feature.properties.perimeter !==
                                        undefined)
                                        myPerim = myPerim + parseFloat(layers._layers[key]._layers[subkey].feature.properties
                                            .perimeter.replace(
                                                /[^0-9]/g,
                                                ''))
                                    myItems++;
                                }
                            }
                        }
                    } else {
                        if (layers._layers[key].feature.properties !== undefined) {
                            if (layers._layers[key].feature.properties.area !== undefined)
                                myArea = myArea + parseFloat(layers._layers[key].feature.properties.area.replace(
                                    /[^0-9]/g, ''))
                            if (layers._layers[key].feature.properties.perimeter !== undefined)
                                myPerim = myPerim + parseFloat(layers._layers[key].feature.properties.perimeter.replace(
                                    /[^0-9]/g, ''))
                            myItems++;
                        }
                    }
                }
            }
        } else {
            if (layers.feature.properties !== undefined) {
                if (layers.feature.properties.area !== undefined)
                    myArea = myArea + parseFloat(layers.feature.properties.area.replace(/[^0-9]/g, ''))
                if (layers.feature.properties.perimeter !== undefined)
                    myPerim = myPerim + parseFloat(layers.feature.properties.perimeter.replace(/[^0-9]/g, ''))
                myItems++;
            }
        }
        return {
            area: myArea,
            perim: myPerim,
            items: myItems
        }
    } //END calcAreaPerimTotals()
    /**
     * Initialize presets from the global groups created by user.
     * Add featurelayers based on created presets
     */
    function initializePresets() {


if (typeof mygroups == 'undefined') {
    var takeoffs = JSON.parse(mytakeoffs);

    mygroups = [];
    for (var i = 0; i < takeoffs.length; i++) {
        var group = {};
        group.groupName = takeoffs[i].title;
        if (typeof takeoffs[i].outline_color !== 'undefined')
            group.stroke = parseColor(takeoffs[i].outline_color).hex;
        if (typeof takeoffs[i].fill_color !== 'undefined')
            group.fillColor = parseColor(takeoffs[i].fill_color).hex;
        if (typeof takeoffs[i].alpha_fill !== 'undefined')
            group.fillOpacity = takeoffs[i].alpha_fill;
        if (typeof takeoffs[i].alpha_color !== 'undefined')
            group.strokeOpacity = takeoffs[i].alpha_color;
        group.weight = 3;
        group.takeoff = true;
        mygroups.push(group);
    }
}
presets = null;
presets = {};
grps = null;
grps = [];
for (i = 0; i < mygroups.length; i++) {
    var g_name = mygroups[i].groupName.toLowerCase();

    g_name = g_name.replace(/\s+/g, '');
    g_name = g_name.replace(/["]/g, "-dq-");
    g_name = g_name.replace(/[']/g, "-sq-");
    g_name = g_name.replace(/[>]/g, "-gt-");
    g_name = g_name.replace(/[<]/g, "-lt-");
    g_name = g_name.replace(/[/]/g, "-bs-");
    g_name = g_name.replace(/[(]/g, "-bst-");
    g_name = g_name.replace(/[)]/g, "-bse-");
    g_name = g_name.replace(/[\\]/g, "-fs-");
    g_name = g_name.replace(/[&]/g, "-and-");
    g_name = g_name.replace(/[,]/g, "-comma-");
    g_name = g_name.replace(/[=]/g, "-equal-");
    g_name = g_name.replace(/[%]/g, "-percent-");
    g_name = g_name.replace(/[+]/g, "-plus-");
    g_name = g_name.replace(/[?]/g, "-question-");
    var category = mygroups[i].category;
    if (typeof category !== 'undefined') {
        if (category !== null)
            category = category.replace(/[\']/g, "'");
    }
    var group_name = mygroups[i].groupName;
    if (typeof group_name !== 'undefined') {
        group_name = group_name.replace(/[\']/g, "'");
        group_name = group_name.replace(/[\\"]/g, '"');
    }
    var takeoff = mygroups[i].takeoff
    if (typeof takeoff !== 'undefined') {
        var group_type = "Map";
    } else {
        var group_type = "Account";
    }
    presets[g_name] = {
        "shortname": g_name,
        "groupID": mygroups[i].groupID,
        "name": group_name,
        "stroke": true,
        "fill": true,
        "color": mygroups[i].stroke,
       // "weight": mygroups[i].weight,
        "weight": 1,
        "opacity": mygroups[i].strokeOpacity,
        "fillColor": mygroups[i].fillColor,
        "fillOpacity": mygroups[i].fillOpacity,
        "groupType": group_type,
        "groupIcon": mygroups[i].groupIcon,
        "groupdefault": mygroups[i].groupdefault,
        "dashArray": mygroups[i].dashArray,
        "dashOffset": mygroups[i].dashOffset,
        "category": category,
        "groupVisible": true,
        "legendVisible": false,
        "labelVisible": false,
        "vectorgroup": true,
        "layervisible": "activated",
        "labelvisible": "deactivated",
        "legendvisible": "deactivated",
        "leafletlayer": L.featureGroup()
    };
    presets[g_name].leafletlayer.on('layeradd', updateGroups);
    presets[g_name].leafletlayer.on('layerremove', updateGroups);

    if (presets[g_name].fillColor == null) {
        presets[g_name].fillColor = mygroups[i].stroke;
    }

    if (category == null) {
        category = "General"
    }
    if (typeof grps[category] == 'undefined') {
        grps[category] = [];

    }

    var temp = {
        name: group_name,
        value: g_name
    };
    grps[category].push(temp);




    $("#selectPresets").append("<option value='" + g_name + "'>" + mygroups[i].groupName +
        "</option>");

    $("#changeGroupPresets").append("<option value='" + g_name + "'>" + mygroups[i].groupName +
        "</option>");
    copyMetadataToFeatureLayer(presets[g_name].leafletlayer, g_name);
    L.Util.setOptions(presets[g_name].leafletlayer, {
        style: presets[g_name].leafletlayer.metadata
    });
    drawnGroups.addLayer(presets[g_name].leafletlayer);
}

presets["none"] = {
    "shortname": "none",
    "name": "Ungrouped",
    "layervisible": "activated",
    "labelvisible": "deactivated",
    "legendvisible": "deactivated",
    "stroke": true,
    "fill": true,
    "groupVisible": true,
    "legendVisible": false,
    "color": "#00ff00",
    "groupType": "Auto",
    "weight": "2",
    "vectorgroup": true,
    "strokeOpacity": "1",
    "fillColor": "#00ff00",
    "fillOpacity": "0.3",
    "leafletlayer": L.featureGroup()
};
var feature = presets["none"].leafletlayer.feature = presets["none"].leafletlayer.feature || {};
feature.type = "FeatureGroup";
feature.properties = feature.properties || {};
presets["none"].leafletlayer.on('layeradd', updateGroups);
presets["none"].leafletlayer.on('layerremove', updateGroups);
copyMetadataToFeatureLayer(presets["none"].leafletlayer, 'none');
drawnGroups.addLayer(presets['none'].leafletlayer);
presets["hole"] = {
    "stroke": "#556666",
   // "weight": 3,
    "weight": 1,
    "strokeOpacity": 1,
    "shortname": "hole",
    "name": "hole",
    "groupType": "Auto",
    "groupVisible": false,
    "vectorgroup": true,
    "legendVisible": false,
    "fillColor": "rgb(255, 153, 0)",
    "fillOpacity": 0.3,
    "leafletlayer": L.featureGroup()
};
var feature = presets["hole"].leafletlayer.feature = presets["hole"].leafletlayer.feature || {};
feature.type = "FeatureGroup";
feature.properties = feature.properties || {};
presets["hole"].leafletlayer.on('layeradd', updateGroups);
presets["hole"].leafletlayer.on('layerremove', updateGroups);
copyMetadataToFeatureLayer(presets["hole"].leafletlayer, 'hole');
drawnGroups.addLayer(presets['hole'].leafletlayer);

presets["photos"] = {
    "shortname": "photos",
    "name": "Photos",
    "layervisible": "activated",
    "labelvisible": "deactivated",
    "legendvisible": "deactivated",
    "groupType": "Auto",
    "photogroup": true,
    "groupVisible": true,
    "leafletlayer": L.featureGroup()
};
presets["photos"].leafletlayer.on('layeradd', updateGroups);
presets["photos"].leafletlayer.on('layerremove', updateGroups);
copyMetadataToFeatureLayer(presets["photos"].leafletlayer, 'photos');
drawnGroups.addLayer(presets['photos'].leafletlayer);
presets["textinternal"] = {
    "shortname": "textinternal",
    "name": "Text",
    "textgroup": true,
    "groupVisible": true,
    "groupType": "Auto",
    "layervisible": "activated",
    "labelvisible": "deactivated",
    "legendvisible": "deactivated",
    "leafletlayer": L.featureGroup()
};
presets["textinternal"].leafletlayer.on('layeradd', updateGroups);
presets["textinternal"].leafletlayer.on('layerremove', updateGroups);
copyMetadataToFeatureLayer(presets["textinternal"].leafletlayer, 'textinternal');
drawnGroups.addLayer(presets['textinternal'].leafletlayer);

presets["markers"] = {
    "shortname": "markers",
    "name": "Markers",
    "markergroup": true,
    "groupVisible": true,
    "groupType": "Auto",
    "layervisible": "activated",
    "labelvisible": "deactivated",
    "legendvisible": "deactivated",
    "leafletlayer": L.featureGroup()
};
presets["markers"].leafletlayer.on('layeradd', updateGroups);
presets["markers"].leafletlayer.on('layerremove', updateGroups);
copyMetadataToFeatureLayer(presets["markers"].leafletlayer, 'markers');
addPresetsInSelect();
initializeGroupList();
drawnGroups.addLayer(presets['markers'].leafletlayer);
map.fireEvent('initlegend');
openMapLayer();


} //END initializePresets()
function addPresetsInSelect() {
		$("#selectPresets").empty();
		$("#headerlayersselect").empty();
		$("#changeGroupPresets").empty();
		$("#headerlayersselect").append('<li><a onclick="map.fireEvent(' + "'headergroupadd'" +
			');">New Layer</a></li>');
		$("#headerlayersselect").append('<li role="separator" class="divider"></li>');
		for (var grp in grps) {

			var g_name = grp.toLowerCase();

			g_name = g_name.replace(/\s+/g, '');
			g_name = g_name.replace(/["]/g, "-dq-");
			g_name = g_name.replace(/[']/g, "-sq-");
			g_name = g_name.replace(/[>]/g, "-gt-");
			g_name = g_name.replace(/[<]/g, "-lt-");
			g_name = g_name.replace(/[/]/g, "-bs-");
			g_name = g_name.replace(/[(]/g, "-bst-");
			g_name = g_name.replace(/[)]/g, "-bse-");
			g_name = g_name.replace(/[\\]/g, "-fs-");
			g_name = g_name.replace(/[&]/g, "-and-");
			g_name = g_name.replace(/[,]/g, "-comma-");
            g_name = g_name.replace(/[=]/g, "-equal-");
            g_name = g_name.replace(/[%]/g, "-percent-");
            g_name = g_name.replace(/[+]/g, "-plus-");
            g_name = g_name.replace(/[?]/g, "-question-");
            
            
			$("#selectPresets").append("<optgroup label='" + grp + "' id='" + g_name + "'> </optgroup>")
			$("#changeGroupPresets").append("<optgroup label='" + grp + "' id='" + grp + "'> </optgroup>")
			$("#headerlayersselect").append("<li class='dropdown-header'>" + grp + "</li>");
			for (i = 0; i < grps[grp].length; i++) {
              
                let value = grps[grp][i].value
                value = value.replace(/[*]/g, "-asterisk-");
                value = value.replace(/[.]/g, "-dot-");
               
				$("#selectPresets #" + g_name).append("<option value='" + value + "'>" + grps[grp][i]
					.name +
					"</option>");
				$("#changeGroupPresets #" + g_name).append("<option id='" + value + "' + 'value='" + grps[
						grp][i].value +
					"'>" + grps[grp][i].name +
					"</option>");

				$("#headerlayersselect").append('<li id="' + value + '"><a onclick="map.fireEvent(' +
					"'headergroupedit'" + ', {grp: ' + "'" + value + "'" + '}' + ');">' + grps[grp][i]
					.name +
					'</a></li>');
				$("#headerlayersselect #" + value).css("background", "linear-gradient(to right, " +
					presets[grps[grp]
						[i].value].color +
					" 4px, " + hexToRGB(
						presets[grps[grp][i].value].fillColor,
						presets[grps[grp][i].value].fillOpacity) + " 4px, " +
					hexToRGB(presets[grps[grp][i].value].fillColor, presets[grps[grp][i].value].fillOpacity) +
					" 12px, rgb(255, 255, 255) 12px, rgb(255, 255, 255)), rgb(255, 255, 255)");

			}



		}
		$("#headerlayersselect").append('<li role="separator" class="divider"></li>');
		$("#headerlayersselect").append('<li><a onclick="map.fireEvent(' + "'showgroupmanager'" +
			');">Layer Manager</a></li>');

	}
    function initializePresetsOld() {
        presets = null;
        presets = {};
        if (typeof bosslm !== 'undefined') {
            parseBossLMPresets();
        } else {
            for (i = 0; i < mygroups.length; i++) {
                var g_name = mygroups[i].groupName.toLowerCase();

                g_name = g_name.replace(/\s+/g, '');
                g_name = g_name.replace(/["]/g, "-dq-");
                g_name = g_name.replace(/[']/g, "-sq-");
                g_name = g_name.replace(/[>]/g, "-gt-");
                g_name = g_name.replace(/[<]/g, "-lt-");
                presets[g_name] = {
                    "shortname": g_name,
                    "groupID": mygroups[i].groupID,
                    "name": mygroups[i].groupName,
                    "stroke": true,
                    "fill": true,
                    "color": mygroups[i].stroke,
                    //"weight": mygroups[i].weight,
                   "weight": 1,
                    "opacity": mygroups[i].strokeOpacity,
                    "fillColor": mygroups[i].fillColor,
                    "fillOpacity": mygroups[i].fillOpacity,
                    "subGroups": mygroups[i].subGroups,
                    "groupType": mygroups[i].groupType,
                    "groupIcon": mygroups[i].groupIcon,
                    "groupdefault": mygroups[i].groupdefault,
                    "dashArray": mygroups[i].dashArray,
                    "dashOffset": mygroups[i].dashOffset,
                    "groupVisible": true,
                    "legendVisible": false,
                    "labelVisible": false,
                    "vectorgroup": true,
                    "layervisible": "activated",
                    "labelvisible": "deactivated",
                    "legendvisible": "deactivated",
                    "leafletlayer": L.featureGroup()
                };
                presets[g_name].leafletlayer.on('layeradd', updateGroups);
                presets[g_name].leafletlayer.on('layerremove', updateGroups);

                if (mygroups[i].subGroups === undefined) {
                    mygroups[i].subGroups = [];
                }
                if (mygroups[i].subGroups === null) {
                    mygroups[i].subGroups = [];
                }
                if (presets[g_name].fillColor == null) {
                    presets[g_name].fillColor = mygroups[i].stroke;
                }
                for (j = 0; j < mygroups[i].subGroups.length; j++) {
                    var g_subname = mygroups[i].subGroups[j].subGroupname.toLowerCase();
                    g_subname = g_subname.replace(/\s+/g, '');
                    g_subname = g_subname.replace(/["]/g, "-dq-");
                    g_subname = g_subname.replace(/[']/g, "-sq-");
                    g_subname = g_subname.replace(/[>]/g, "-gt-");
                    g_subname = g_subname.replace(/[<]/g, "-lt-");
                    $("#selectsubPresets").append("<option value='" + g_subname + "'>" + mygroups[i].subGroups[j].subGroupname +
                        "</option>");
                }
                $("#selectPresets").append("<option value='" + g_name + "'>" + mygroups[i].groupName +
                    "</option>");
                $("#changeGroupPresets").append("<option value='" + g_name + "'>" + mygroups[i].groupName +
                    "</option>");
                copyMetadataToFeatureLayer(presets[g_name].leafletlayer, g_name);
                L.Util.setOptions(presets[g_name].leafletlayer, {
                    style: presets[g_name].leafletlayer.metadata
                });
                drawnGroups.addLayer(presets[g_name].leafletlayer);
            }
        }
        presets["none"] = {
            "shortname": "none",
            "name": "Ungrouped",
            "layervisible": "activated",
            "labelvisible": "deactivated",
            "legendvisible": "deactivated",
            "stroke": true,
            "fill": true,
            "groupVisible": true,
            "legendVisible": false,
            "color": "#00ff00",
          //  "weight": "2",
            "weight": "1",
            "vectorgroup": true,
            "strokeOpacity": "1",
            "fillColor": "#00ff00",
            "fillOpacity": "0.3",
            "leafletlayer": L.featureGroup()
        };
        var feature = presets["none"].leafletlayer.feature = presets["none"].leafletlayer.feature || {};
        feature.type = "FeatureGroup";
        feature.properties = feature.properties || {};
        presets["none"].leafletlayer.on('layeradd', updateGroups);
        presets["none"].leafletlayer.on('layerremove', updateGroups);
        copyMetadataToFeatureLayer(presets["none"].leafletlayer, 'none');
        drawnGroups.addLayer(presets['none'].leafletlayer);
        presets["hole"] = {
            "stroke": "#556666",
           // "weight": 3,
            "weight": 1,
            "strokeOpacity": 1,
            "shortname": "hole",
            "name": "hole",
            "groupVisible": false,
            "vectorgroup": true,
            "legendVisible": false,
            "fillColor": "rgb(255, 153, 0)",
            "fillOpacity": 0.3,
            "leafletlayer": L.featureGroup()
        };
        var feature = presets["hole"].leafletlayer.feature = presets["hole"].leafletlayer.feature || {};
        feature.type = "FeatureGroup";
        feature.properties = feature.properties || {};
        presets["hole"].leafletlayer.on('layeradd', updateGroups);
        presets["hole"].leafletlayer.on('layerremove', updateGroups);
        copyMetadataToFeatureLayer(presets["hole"].leafletlayer, 'hole');
        drawnGroups.addLayer(presets['hole'].leafletlayer);

        presets["photos"] = {
            "shortname": "photos",
            "name": "Photos",
            "layervisible": "activated",
            "labelvisible": "deactivated",
            "legendvisible": "deactivated",
            "photogroup": true,
            "groupVisible": true,
            "leafletlayer": L.featureGroup()
        };
        presets["photos"].leafletlayer.on('layeradd', updateGroups);
        presets["photos"].leafletlayer.on('layerremove', updateGroups);
        copyMetadataToFeatureLayer(presets["photos"].leafletlayer, 'photos');
        drawnGroups.addLayer(presets['photos'].leafletlayer);
        presets["text"] = {
            "shortname": "text",
            "name": "Text",
            "textgroup": true,
            "groupVisible": true,
            "layervisible": "activated",
            "labelvisible": "deactivated",
            "legendvisible": "deactivated",
            "leafletlayer": L.featureGroup()
        };
        presets["text"].leafletlayer.on('layeradd', updateGroups);
        presets["text"].leafletlayer.on('layerremove', updateGroups);
        copyMetadataToFeatureLayer(presets["text"].leafletlayer, 'text');
        drawnGroups.addLayer(presets['text'].leafletlayer);

        presets["markers"] = {
            "shortname": "markers",
            "name": "Markers",
            "markergroup": true,
            "groupVisible": true,
            "layervisible": "activated",
            "labelvisible": "deactivated",
            "legendvisible": "deactivated",
            "leafletlayer": L.featureGroup()
        };
        presets["markers"].leafletlayer.on('layeradd', updateGroups);
        presets["markers"].leafletlayer.on('layerremove', updateGroups);
        copyMetadataToFeatureLayer(presets["markers"].leafletlayer, 'markers');
        initializeGroupList();
        drawnGroups.addLayer(presets['markers'].leafletlayer);
        map.fireEvent('initlegend');
        openMapLayer();
    } //END initializePresets()
    function initializeGroupList() {
        $('#group-list').empty();
        for (var preset in presets) {
            if (presets[preset].groupID !== undefined || presets[preset].groupID != null)
                $('#group-list').append("<option value='" + presets[preset].name + "'>")
        }
    }

    function copyMetadataToFeatureLayer(layer, group) {
        layer.metadata = layer.metadata || {}
        for (var property in presets[group]) {
            if (presets[group].hasOwnProperty(property)) {
                if (property != 'leafletlayer') {
                    layer.metadata[property] = presets[group][property]
                }
            }
        }
    }

    function hexToRGB(hex, alpha) {
        var r = parseInt(hex.slice(1, 3), 16),
            g = parseInt(hex.slice(3, 5), 16),
            b = parseInt(hex.slice(5, 7), 16);

        if (alpha) {
            return "rgba(" + r + ", " + g + ", " + b + ", " + alpha + ")";
        } else {
            return "rgb(" + r + ", " + g + ", " + b + ")";
        }
    }

    function updateGroups(e) {
        layer = e.layer;
        groupId = e.target.metadata.shortname;
        type = e.type;
        groupId2 = groupId.replace(/[*]/g, "-asterisk-");
        groupId2 = groupId.replace(/[?]/g, "-question-");
        if (type == 'layeradd') {
            groupdata = calcAreaPerimTotals(presets[groupId].leafletlayer);
            

            if (groupdata.items == 1 && $('#' + groupId2 + '-card').length == 0) {
                var context = {
                    groupName: presets[groupId].name,
                    groupId: groupId,
                    fillColor: presets[groupId].fillColor,
                    color: presets[groupId].color,
                    cogEnabled: groupId != "none"
                };
                var html = groupTemplate(context);
            }
            grouparea = numeral(groupdata.area).format('0,0');
            grouparea = grouparea + ' sqft';
            groupperim = numeral(_round(groupdata.perim, 2)).format('0,0');
            groupperim = groupperim + ' ft';
            if (presets[groupId].fillColor && presets[groupId].color && presets[groupId].fillOpacity) {
                var fillColor = presets[groupId].fillColor;
                var color = presets[groupId].color;
                var alpha = presets[groupId].fillOpacity;
                $('#' + groupId2 + '-card').css("background", "linear-gradient(to left, " + color + " 4px, " +
                    hexToRGB(fillColor,
                        alpha) + " 4px, " +
                    hexToRGB(fillColor, alpha) +
                    " 12px, rgb(224, 224, 224) 12px, rgb(224, 224, 224)), rgb(244, 244, 244)");
            }
            $('#' + groupId2 + '-card .group-area').html("<i class='fa fa-th'>&nbsp;&nbsp;</i>" + grouparea);
            $('#' + groupId2 + '-card .group-perim').html("&nbsp;&nbsp;<i class='fa fa-arrows-v'>&nbsp;&nbsp;</i>" +
                groupperim);
            var context = {
                layerName: layer.feature.properties.name || layer.feature.properties.type + layer.feature.properties
                    .number,
                layerId: layer._leaflet_id,
                groupId: groupId,
                layerArea: layer.feature.properties.area,
                layerPerim: layer.feature.properties.perimeter,
                rect: layer.feature.properties.type == "Rectangle",
                polygon: layer.feature.properties.type == "Polygon",
                line: layer.feature.properties.type == "Line",
                circle: layer.feature.properties.type == "Circle"
            };
            var html = layerTemplate(context);

            $('#' + groupId2 + "-items").append(html);
        } else {

            groupdata = calcAreaPerimTotals(presets[groupId].leafletlayer);

            if (groupdata.items == 0) {
                $('#' + groupId2 + '-card').remove();
                return;
            }

            grouparea = numeral(groupdata.area).format('0,0');
            grouparea = grouparea + ' sqft';
            groupperim = numeral(_round(groupdata.perim, 2)).format('0,0');
            groupperim = groupperim + ' ft';
            $('#' + groupId2 + '-card .group-area').html("<i class='fa fa-th'>&nbsp;&nbsp;</i>" + grouparea);
            $('#' + groupId2 + '-card .group-perim').html("&nbsp;&nbsp;<i class='fa fa-arrows-v'>&nbsp;&nbsp;</i>" +
                groupperim);

        }
    }
    /**
     * Add photomarkers
     * Add featurelayers based on created presets
     */
    function addPhotoMarkers(url, desc, date, tags = '', iconUrl, markerSize, lat1, lng1, lat2,
        lng2, buildname) {
        numBldgs = numBldgs + 1;
        var photoLinks = "";
        var photoInfo = '';
        var picURL = '';
        picURL = "<a href='" + url +
            "' target='_blank' ><img style='max-height:200px; max-width:300px;'   src='" +
            url + "' > </a>";
        var caption = desc;
        photoInfo += date + '<br/>';
        if (caption.length > 0) {
            photoInfo += '<br/><span contenteditable style="border: 1px dashed blue">' + caption +
                '</span><br/>';
        }
        photoLinks = picURL + '<br/>';
        //to determine which icon to use, check the tags
        var markerUrl, markerSize;
        if (iconUrl.length > 0) {
            markerUrl = iconUrl;
            markerSize = markerSize;
        } else if (tags.length > 0) {
            if (tags.indexOf('pre') !== -1) {
                markerUrl = LNumIconSquareRed.options.iconUrl;
                markerSize = LNumIconSquareRed.options.iconSize;
            } else if (tags.indexOf('hazard') !== -1) {
                markerUrl = LNumIconDiamondBlack.options.iconUrl;
                markerSize = LNumIconDiamondBlack.options.iconSize;
            } else if (tags.indexOf('new') !== -1) {
                markerUrl = LNumIconStarYellow.options.iconUrl;
                markerSize = LNumIconStarYellow.options.iconSize;
            } else if (tags.indexOf('post') !== -1) {
                markerUrl = LNumIconGreen.options.iconUrl;
                markerSize = LNumIconGreen.options.iconSize;
            }
        } else {
            markerUrl = LNumIconBlue.options.iconUrl;
            markerSize = LNumIconBlue.options.iconSize;
        }
        var lat, lng;
        if ((lat2 == '') || (lat2 == '0') || (lat2 == 0)) {
            lat = lat1;
            lng = lng1;
        } else {
            lat = lat2;
            lng = lng2;
        }
        //var marker = new L.Marker([imglist[i][5], imglist[i][6]], {
        var marker = new L.Marker([lat, lng], {
            id: numBldgs,
            icon: new L.NumberedDivIcon({
                iconUrl: markerUrl,
                iconSize: markerSize,
                number: numBldgs
            })
            //icon:	new L.NumberedDivIcon({iconUrl: LNumIconDiamondBlack.options.iconUrl, iconSize: LNumIconDiamondBlack.options.iconSize, number: numBldgs})
        })
        // Create an element to hold all your text and markup
        var container = $('<div />');
        // Delegate all event handling for the container itself and its contents to the container
        //container.on('click', '.PicLink', function() { });
        // Insert whatever you want into the container, using whichever approach you prefer
        var title =
            '<div  class="popuptitle" style="font-size:14px; text-align:center; font-weight:bold; margin-bottom:5px; display:none;" >Image</div>';
        container.html(title + photoLinks + photoInfo);
        //container.append($('<span class="bold">').text(" :)"))
        // Insert the container into the popup
        //marker.bindPopup(container[0]);
        //**********************************************************************
        var layer = marker;
        var feature = layer.feature = layer.feature || {};
        feature.type = "Feature";
        feature.properties = feature.properties || {};
        if (layer instanceof L.Marker) {
            feature.properties["type"] = 'Photo';
            feature.properties["location"] = layer._latlng;
            feature.properties["imageURL"] = url;
            feature.properties["markerURL"] = markerUrl;
            feature.properties["markerSize"] = markerSize;
            feature.properties["number"] = numBldgs;
            feature.properties["description"] = desc;
            feature.properties["date"] = date;
            feature.properties["buildname"] = buildname;
        }

        marker.on('click', onPolyClick);

        marker.on('dragend', function (event) {
            var marker = event.target;
            var position = marker.getLatLng();
            //event.target.setIcon(LwarningIcon);
            marker.setLatLng(position, {
                id: numBldgs,
                draggable: 'true'
            });
        });
        marker.on('popupopen', function () {
            var marker = this;
            $(".marker-colors").on("click", function () {
                marker.setIcon(eval($(this).data("id")));
                //marker.setIcon(LwarningIcon);
            })
        });


        obj = photoURL2GroupName(marker.feature.properties.markerURL);
        if (obj) {


           let preset = addPhotoPreset(obj.shortname, obj.name);

            preset.leafletlayer.addLayer(layer);
        } else
            presets["photos"].leafletlayer.addLayer(layer);


    } //end addphotomarkers
    //Handle click on item
    onPolyClick = function (event) {
        selectedItem = event.target;
        var props = event.target.feature.properties;

        L.DomEvent.stopPropagation(event);
        $("#text_shapetype").show();
        $("#rowTextArea").show();
        $("#rowTextSize").hide();
        $("#rowPhoto").hide();
        $("#rowPhotoDate").hide();


        $("#rowArrow").hide();
        $("#rowName").show();
        $("#rowPresets").show();
        $("#selectPresets").prop("disabled", true);
        $("#cellTextArea").prop("disabled", true);
        $("#cellTextArea").empty();
        $("#rowIconNumber").hide();
        $("#rowCopy").hide();
        $("#deleteButton").hide();



        var color = event.target.options.color;
        $("#propertiesName").val(props.name);

        var mytemp = props.description;
        if(typeof mytemp !== "undefined") {
        mytemp = mytemp.replace(/<br[^>]*>/g, "\n");
        mytemp = mytemp.replace(
            /((http|https|ftp|ftps)\:\/\/[a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,3}(\/\S*)?)/g,
            '<a href="$1" target="_blank">$1</a>'
        );
        $("#cellTextArea").html(mytemp); //Set Description
        }
        $("#text_shapetype").text(props.type); //Set Shape Type

        if (typeof props.preset !== "undefined")
            $("#selectPresets").val(props.preset);
        else
            $("#selectPresets").val($("#selectPresets").find('option').first().val());

        if (props.type == 'Polygon') {


            $("#rowIcon").hide();
            $("#rowArea").show();
            $("#text_area").text(props.area); //Set Area
            $("#rowPerimeter").show();
            $("#text_perim").text(props.perimeter); //Set Perimeter
            $("#text_name").text(props.name); //Set Name
            $("#showPaletteOnly").spectrum("set", color); //Set Color Picker

            if (selectedItem.options.fillOpacity > 0)
                $("#propertiesFillOpacityValue").val(0.7);
            else
                $("#propertiesFillOpacityValue").val(selectedItem.options.fillOpacity);

            
            $("#propertiesStrokeWidthValue").val(1);
            if (typeof props.subtract !== "undefined")
                $("#parentShape").val(props.subtract);
            else
                $("#parentShape").val('');


        } else if (props.type == 'Rectangle') {
            $("#rowIcon").hide();
            $("#rowArea").show();
            $("#text_area").text(props.area); //Set Area
            $("#rowPerimeter").show();
            $("#text_perim").text(props.perimeter); //Set Perimeter
            $("#text_name").text(props.name); //Set Name
            $("#showPaletteOnly").spectrum("set", color); //Set Color Picker

           if (selectedItem.options.fillOpacity > 0)
                $("#propertiesFillOpacityValue").val(0.7);
            else
                $("#propertiesFillOpacityValue").val(selectedItem.options.fillOpacity);


            $("#propertiesStrokeWidthValue").val(1);
            if (typeof props.subtract !== "undefined")
                $("#parentShape").val(props.subtract);
            else
                $("#parentShape").val('');


        } else if (props.type == 'Line') {
            //$("#rowArrow").show();

            if (typeof props.arrow !== "undefined")
                $("#selectArrow").val(props.arrow);
            else
                $("#selectArrow").val($("#selectArrow").find('option').first().val());


            $("#rowIcon").hide();
            $("#rowArea").hide();
            $("#text_area").text(props.area); //Set Area
            $("#rowPerimeter").show();
            $("#text_perim").text(props.perimeter); //Set Perimeter
            $("#text_name").text(props.name); //Set Name
            $("#showPaletteOnly").spectrum("set", color); //Set Color Picker
            $("#propertiesStrokeWidthValue").val(1);
            $("#rowOpacity").hide();
            $("#rowHole").hide();
        } else if (props.type == 'Circle') {
            $("#rowHole").hide();
            $("#rowIcon").hide();
            $("#rowArea").show();
            $("#text_area").text(props.area); //Set Area
            $("#rowPerimeter").show();
            $("#text_perim").text(props.perimeter); //Set Perimeter
            $("#text_name").text(props.myId); //Set Name
            $("#showPaletteOnly").spectrum("set", color); //Set Color Picker
            $("#propertiesStrokeWidthValue").val(1);

           if (selectedItem.options.fillOpacity > 0)
                $("#propertiesFillOpacityValue").val(0.7);
            else
                $("#propertiesFillOpacityValue").val(selectedItem.options.fillOpacity);
            $("#propertiesStrokeWidthValue").val(1);
            if (typeof props.subtract !== "undefined")
                $("#parentShape").val(props.subtract);
            else
                $("#parentShape").val('');

        } else if (props.type == 'Marker') {
            $("#rowAddPreset").hide();
            $("#rowPresets").hide();
            $("#rowArea").hide();
            $("#rowPerimeter").hide();
            $("#rowColor").hide();
            $("#rowStrokeWidth").hide();
            $("#rowOpacity").hide();
            $("#rowHole").hide();
            $("#text_name").text(props.name); //Set Name
            $("#showPaletteOnly").spectrum("set", color); //Set Color Picker
            //   $("#rowIcon").show();
            $("#cellIcon").attr("src", props.markerURL);
 


        } else if (props.type == 'Photo') {
            $("#rowAddPreset").hide();
            $("#rowPresets").hide();
            $("#rowPhoto").show();
            $("#cellPhoto").attr("src", props.imageURL);
            $("#cellPhoto").attr("onclick", 'window.open(this.src)');
            $("#rowPhotoDate").show();
            $("#rowClone").hide();
            $("#text_date").text(props.date); //Set Area
            // $("#rowIcon").show();
            $("#cellIcon").attr("src", props.markerURL);


            mytemp = props.description;
            mytemp = mytemp.replace(/<br[^>]*>/g, "\n");
            mytemp = mytemp.replace(
                /((http|https|ftp|ftps)\:\/\/[a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,3}(\/\S*)?)/g,
                '<a href="$1" target="_blank">$1</a>'
            );
            $("#cellTextArea").html(mytemp);
            $("#rowPerimeter").hide();
            $("#rowColor").hide();
            $("#rowStrokeWidth").hide();
            $("#rowOpacity").hide();
            $("#rowHole").hide();
            $("#rowArea").hide();
            //$("#rowIconNumber").show();
            // $("#iconNumber").text(props.number); //Set Area
            $("#rowName").hide();
        } else if (props.type == 'Text') {
            $("#rowAddPreset").hide();
            $("#rowPresets").hide();
            $("#rowPhoto").hide();
            $("#rowPhotoDate").hide();
            $("#rowClone").hide();

            $("#rowIcon").hide();
            $("#rowDescription").hide();
            $("#rowArea").hide();
            $("#rowPerimeter").hide();
            $("#rowColor").hide();
            $("#rowStrokeWidth").hide();
            $("#rowOpacity").hide();
            $("#rowHole").hide();
            $("#rowName").hide();
            //	$( "#rowType" ).hide();
            //	$( "#rowService" ).hide();
            $("#rowTextArea").show();
            mytemp = props.description;
            mytemp = mytemp.replace(/<br[^>]*>/g, "\n");
            mytemp = mytemp.replace(
                /((http|https|ftp|ftps)\:\/\/[a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,3}(\/\S*)?)/g,
                '<a href="$1" target="_blank">$1</a>'
            );
            $("#cellTextArea").html(mytemp);
            //$("#rowTextSize").show();
            $("#propertiesFontSizeValue").val(props.fontSize);
            //$("#propertiesStrokeWidthValue").val(props.strokeWeight);
        }
        displayfeatureeditor(selectedItem, props.type);

    };

    function loadBuildingMarkers() {
        var bldglats = [];
        var bldglngs = [];
        //var bldgmarkers;
        //bldgmarkers = new L.FeatureGroup();
        for (i = 0; i < bldglist.length; i++) {
            var bldgicon = LredIcon;
            bldgicon = LgreenIcon;
            var marker = L.marker([bldglist[i].mb_lat, bldglist[i].mb_long], {
                icon: bldgicon
            }).addTo(map);
            var container = $('<div />');
            container.html(bldglist[i].mb_nickname);
            marker.bindPopup(container[0]);
            //bldgmarkers.addLayer(marker);
            var layer = marker;
            var feature = layer.feature = layer.feature || {};
            feature.type = "Feature";
            feature.properties = feature.properties || {};
            if (layer instanceof L.Marker) {
                feature.properties["type"] = 'Marker';
                feature.properties["location"] = layer._latlng;
                feature.properties["markerSize"] = layer.options.icon.options.iconSize;
                feature.properties["markerURL"] = layer.options.icon.options.iconUrl;
                feature.properties["name"] = bldglist[i].mb_nickname;
                feature.properties["id"] = layer._leaflet_id;
            }
            layer.on('click', onPolyClick);
            drawnItems.addLayer(marker);
            bldglats.push(bldglist[i].mb_lat);
            bldglngs.push(bldglist[i].mb_long);
            if (i > (Number(bldglist.length) - 2)) {
                setbounds(map, bldglats, bldglngs);
            }
        }
        //drawnItems.addLayer(bldgmarkers);
        map.fitBounds(mapbounds);
    } // END loadBuildingMarkers
    function addNewTextArea(text = 'Click to Edit Text<br>Drag to Move Text', location = map.getCenter(),
        fontSize = 9) {
        //TEXT LABELS
        fontSize = Math.round(fontSize + ((22 - map.getZoom()) * 1 / matrix[map.getZoom()]));
        var k = [(fontSize / 2) * 50 * matrix[map.getZoom()], (fontSize / 2) * 20 * matrix[map.getZoom()]];
        var label = new L.Marker(location, {
            icon: new L.DivIcon({
                className: 'allTextarea',
                html: text,
                iconSize: k,
                iconAnchor: [0, 0],
            }),
            draggable: 'false'
        });
        var layer = label;
        var feature = layer.feature = layer.feature || {};
        feature.type = "Feature";
        feature.properties = feature.properties || {};
        feature.properties["type"] = 'Text';
        feature.properties["fontSize"] = fontSize;
        feature.properties["description"] = text;
        presets['text'].leafletlayer.addLayer(layer);

        layer.on('click', onPolyClick);
        //END TEXT LABELS
        $(".allTextarea").fitText();
    }

    function getCenterofShape(mylayer) {
        var latlngCenter;
        if (mylayer.feature.properties.type != "Circle" && mylayer.feature.properties.type != "Marker" && mylayer.feature
            .properties
            .type !=
            "Photo" && mylayer.feature.properties.type != "Text") {
            var bounds = mylayer.getBounds();
            latlngCenter = bounds.getCenter();
        } else {
            latlngCenter = mylayer._latlng;
        }
        return latlngCenter;
    }

    function setHighlight(layer) {
        // Check if something's highlighted, if so unset highlight
        if (highlight) {
            unsetHighlight(highlight);
        }
        if (layer.feature.properties.type == "Marker") {
            layer._icon.style.borderRadius = "50%";
            layer._icon.style.backgroundColor = "white";
        } else
            layer._path.setAttribute("filter", "url(#outline_selected)");
        highlight = layer;
    }

    function unsetHighlight(layer) {
        // Set default style and clear variable
        if (highlight != null) {
            if (highlight.feature.properties.type == "Marker") {
                highlight._icon.style.borderRadius = "0px";
                highlight._icon.style.backgroundColor = "";
            } else
                highlight._path.setAttribute("filter", "");
        }
        highlight = null;
    }
    //TODO: Change this function to use new features.
    function getSubtractAreas(shapeName) {
        var subtractArea = 0;
        drawnGroups.eachLayer(function (player) {
            player.eachLayer(function (layer) {
                if (typeof layer.feature.properties.subtract !== "undefined") {
                    if ((layer.feature.properties.subtract == shapeName) && (shapeName.length > 0)) {
                        if (layer.feature.properties.area) {
                            var areaInCell = layer.feature.properties.area.substr(0, (layer.feature
                                .properties.area
                                .length -
                                5));
                            subtractArea = subtractArea + parseInt(areaInCell.replace(/,/g, ''), 10);
                        }
                    }
                }
            });
        });
        return subtractArea;
    }

    map.on(L.Draw.Event.CREATED, function (event) {
        var layer = event.layer,
            layerType = event.layerType;
        var feature = layer.feature = layer.feature || {};
        feature.type = "Feature";
        feature.properties = feature.properties || {};
        shapeCounter += 1;
        feature.properties["id"] = shapeCounter;
        feature.properties["name"] = '';
        feature.properties["preset"] = globalSelectedGroup;
        if (clone != '') {
            feature.properties["preset"] = clone;
            clone = '';
        }

        updateLayerProps(layer);
        layer.on('click', onPolyClick);
        addLayerToMap(layer);
        layer.fireEvent('click');
    });

    function findObjectByKey(array, key, value) {
        for (var i = 0; i < array.length; i++) {
            if (array[i][key] === value) {
                return array[i];
            }
        }
        return null;
    }

    function addLayerToMap(layer) {
        if (layer instanceof L.Marker) {
            obj = iconURL2GroupName(layer.feature.properties.markerURL);
            if (obj) {
                preset = addMarkerPreset(obj.shortname, obj.name);
                preset.leafletlayer.addLayer(layer);
            } else
                presets["markers"].leafletlayer.addLayer(layer);
        } else {
            presets[globalSelectedGroup].leafletlayer.addLayer(layer);
        }
    }

    function addMarkerPreset(shortname, name) {
        if (presets.hasOwnProperty(shortname))
            return presets[shortname];
        else {
            presets[shortname] = {};
            presets[shortname].name = name;
            presets[shortname].shortname = shortname;
            presets[shortname].visible = true;
            presets[shortname].markergroup = true;
            presets[shortname].leafletlayer = L.featureGroup();
            presets[shortname].leafletlayer.on('layeradd', updateGroups);
            presets[shortname].leafletlayer.on('layerremove', updateGroups);
            copyMetadataToFeatureLayer(presets[shortname].leafletlayer, shortname);
            drawnGroups.addLayer(presets[shortname].leafletlayer);
            return presets[shortname];
        }
    }

    function addPhotoPreset(shortname, name) {
        if (presets.hasOwnProperty(shortname))
            return presets[shortname];
        else {
            presets[shortname] = {};
            presets[shortname].name = name;
            presets[shortname].shortname = shortname;
            presets[shortname].visible = true;
            presets[shortname].groupVisible = true;
            presets[shortname].photogroup = true;
            presets[shortname].leafletlayer = L.featureGroup();
            presets[shortname].leafletlayer.on('layeradd', updateGroups);
            presets[shortname].leafletlayer.on('layerremove', updateGroups);
            copyMetadataToFeatureLayer(presets[shortname].leafletlayer, shortname);
            drawnGroups.addLayer(presets[shortname].leafletlayer);
            return presets[shortname];
        }
    }

    function iconURL2GroupName(url) {
        var obj = findObjectByKey(myicons.Markers.type[0].icons, "iconurl", url);
        var obj2 = findObjectByKey(myicons.Markers.type[1].icons, "iconurl", url);
        if (obj || obj2) {
            g_name = obj != null ? obj.icondesc : obj2.icondesc;
            var g_sname = g_name.toLowerCase();
            g_sname = g_sname.replace(/\s+/g, '');
            return {
                "shortname": g_sname,
                "name": g_name
            }
        } else
            return null;

    }

    function photoURL2GroupName(url) {
        switch (url) {
            case '/images/marker_hole_diamond_black.svg':
                return {
                    "shortname": 'picturediamondblack',
                    "name": 'Pictures - Diamond Black'
                }
                break;
            case '/images/marker-hole-green.svg':
                return {
                    "shortname": 'picturegreen',
                    "name": 'Pictures - Green'
                }
                break;
            case '/images/marker-hole-blue.svg':
                return {
                    "shortname": 'pictureblue',
                    "name": 'Pictures - Blue'
                }
                break;
            case '/images/marker_hole_star_yellow1.svg':
                return {
                    "shortname": 'pictureyellow',
                    "name": 'Pictures - Yellow'
                }
                break;
            case '/images/marker_hole_square_red.svg':
                return {
                    "shortname": 'picturesquarered',
                    "name": 'Pictures - Square Red'
                }
                break;

            default:
                return null;
                break;
        }
    }
    map.on(L.Draw.Event.DRAWSTART, function (event) {

        drawControl.setDrawingOptions({
            rectangle: {
                shapeOptions: _.omitBy(presets[globalSelectedGroup], _.isObject)
            },
            polyline: {
                shapeOptions: _.omitBy(_.omit(presets[globalSelectedGroup], ['fill', 'fillColor',
                    'fillOpacity'
                ]), _.isObject)
            },
            polygon: {
                shapeOptions: _.omitBy(presets[globalSelectedGroup], _.isObject)
            },
            circle: {
                shapeOptions: _.omitBy(presets[globalSelectedGroup], _.isObject)
            }
        });
    });
    map.on("editable:enable", function (event) {
        if (event.layer.feature.properties.type == "Photo" || event.layer.feature.properties.type ==
            "Marker") {
            L.DomUtil.addClass(event.layer._icon, 'leaflet-edit-marker-selected');
            icon = event.layer._icon;
            offset = 4;
            iconMarginTop = parseInt(icon.style.marginTop, 10) - offset;
            iconMarginLeft = parseInt(icon.style.marginLeft, 10) - offset;
            event.layer._icon.style.marginTop = iconMarginTop + 'px';
            event.layer._icon.style.marginLeft = iconMarginLeft + 'px';
        }
    });
    map.on("editable:disable", function (event) {

        if (event.layer.feature.properties.type == "Photo" || event.layer.feature.properties.type ==
            "Marker") {
            if (event.layer._icon) {
                L.DomUtil.removeClass(event.layer._icon, 'leaflet-edit-marker-selected');
                icon = event.layer._icon;
                offset = 4;
                iconMarginTop = parseInt(icon.style.marginTop, 10) + offset;
                iconMarginLeft = parseInt(icon.style.marginLeft, 10) + offset;
                event.layer._icon.style.marginTop = iconMarginTop + 'px';
                event.layer._icon.style.marginLeft = iconMarginLeft + 'px';
            }
        }
    });

    map.on('zoomend', function () {
        var currentZoom = map.getZoom();
        $(".allLabels").css("font-size", currentZoom - 8);
        if (typeof presets !== 'undefined') {
            if (typeof presets["textinternal"] !== 'undefined') {
                if (typeof presets["textinternal"].leafletlayer !== 'undefined') {

                    presets["textinternal"].leafletlayer.eachLayer(function (layer) {
                        if (layer.feature.properties.type == "Text") {
                            var zoom = map.getZoom();
                            var icon = layer.options.icon;
                            var orignal_size = icon.options.iconSize;
                            var orignal_anchor = icon.options.iconAnchor
                           
                            if ((layer.feature.properties.fontSize - (22 - zoom) )<= 0) {

                                $(layer._icon).hide();
                            } else {
                                $(layer._icon).show();
                            }
                            var to = [(layer.feature.properties.fontSize / 2) * 50 * matrix[map
                                .getZoom()],
                                (layer.feature.properties.fontSize /
                                    2) * 20 *
                                matrix[map.getZoom()]
                            ];
                            icon.options.iconSize = to;
                            if (typeof layer.feature.properties.desctype !== "undefined") {
                                console.log(layer.feature.properties.desctype)
                                console.log(layer.feature.properties.description)
                                if (layer.feature.properties.desctype == 'none')
                                    icon.options.html = layer.feature.properties.description
                                else {
                                    var obj = JSON.parse(layer.feature.properties.description);

                                    temphtml = "<table class='textgrid'>"
                                    temphtml += '<thead><tr><th>Name</th><th>Value</th></tr></thead>';
                                    for (var k in obj) {
                                        if (obj.hasOwnProperty(k)) {
                                            temphtml += '<tr><td>' + k + '</td><td>' + obj[k] +
                                                '</td></tr>';
                                        }
                                    }
                                    temphtml += "</table>";
                                    icon.options.html = temphtml;

                                }
                            } else
                                icon.options.html = layer.feature.properties.description

                            layer.setIcon(icon);
                            $(".allTextarea").fitText();

                        }
                    });
                }
            }
        }
    });
 
    var colorPalette = ["#000000", "#434343", "#666666", "#cccccc", "#d9d9d9", "#ffffff", "#980000", "#ff0000",
        "#ff9900",
        "#ffff00", "#00ff00", "#00ffff", "#4a86e8", "#0000ff", "#9900ff", "#ff00ff", "#e6b8af", "#f4cccc",
        "#fce5cd",
        "#fff2cc", "#d9ead3", "#d0e0e3", "#c9daf8", "#cfe2f3", "#d9d2e9", "#ead1dc", "#dd7e6b", "#ea9999",
        "#f9cb9c",
        "#ffe599", "#b6d7a8", "#a2c4c9", "#a4c2f4", "#9fc5e8", "#b4a7d6", "#d5a6bd", "#cc4125", "#e06666",
        "#f6b26b",
        "#ffd966", "#93c47d", "#76a5af", "#6d9eeb", "#6fa8dc", "#8e7cc3", "#c27ba0", "#a61c00", "#cc0000",
        "#e69138",
        "#f1c232", "#6aa84f", "#45818e", "#3c78d8", "#3d85c6", "#674ea7", "#a64d79", "#5b0f00", "#660000",
        "#783f04",
        "#7f6000", "#274e13", "#0c343d", "#1c4587", "#073763", "#20124d", "#4c1130", "#9e8000", "#9900b7",
        "#9e2300",
        "#b223b7", "#556666", "#69aeb7", "#b2aeb7", "#9e80b7", "#008000"
    ]


    function removeNulls(obj) {
        var isArray = obj instanceof Array;
        for (var k in obj) {
            if (obj[k] === null) isArray ? obj.splice(k, 1) : delete obj[k];
            else if (typeof obj[k] == "object") removeNulls(obj[k]);
            if (isArray && obj.length == k) removeNulls(obj);
        }
        return obj;
    }

    function cleanSavedData(sData) {
        var myData = JSON.parse(JSON.stringify(sData));
        return new Promise(resolve => {
            var iDone = false;
            for (var lll = 0; lll < myData.length; lll++) {
                var sDataFeature = myData[lll].features;
                if (typeof sDataFeature != 'undefined') {

                    for (var jjj = 0; jjj < sDataFeature.length; jjj++) {
                        if (typeof sDataFeature[jjj].geometry != 'undefined') {
                            if (sDataFeature[jjj].geometry.type == 'Polygon'  ) {
                                var sDataCoords = sDataFeature[jjj].geometry.coordinates;
                                sDataCoords = removeNulls(sDataCoords)
                            }
                            if (sDataFeature[jjj].geometry.type == "MultiPolygon"  ) {
                                var sDataCoords = sDataFeature[jjj].geometry.coordinates;
                                sDataCoords = removeNulls(sDataCoords)
                            }

                        }
                    }
                }
            }
            resolve(myData);

        });

    }

    async function openv2format(savedData) {
        let chblayer = false;
            let chblayertype = '';
            let chblayerimage = undefined;
            let chblayerdate = undefined;
            let chblayerprovider = undefined;
            let chbeagleviewlayerid = undefined;
            let chbeagleviewpurchaseid = undefined;
            let chbeagleviewtilematrixset = undefined;
            let chbeagleviewminzoom = undefined;
            let chbeagleviewmaxzoom = undefined;
        savedData = await cleanSavedData(savedData);
        if (typeof savedData[0] != 'undefined') {
                if (typeof savedData[0].metadata !== 'undefined') {
                    if (typeof savedData[0].metadata.baselayer !== 'undefined') {

                        chblayertype = savedData[0].metadata.baselayer;
                        chblayerimage = savedData[0].metadata.image;
                        chblayerdate = savedData[0].metadata.date;
                        chblayerprovider = savedData[0].metadata.provider;

                        // Extract EagleView specific metadata if present
                        if (savedData[0].metadata.provider === 'eagleview') {
                            chbeagleviewlayerid = savedData[0].metadata.eagleview_layer_id;
                            chbeagleviewpurchaseid = savedData[0].metadata.eagleview_purchase_id;
                            chbeagleviewtilematrixset = savedData[0].metadata.eagleview_tile_matrix_set;
                            chbeagleviewminzoom = savedData[0].metadata.eagleview_min_zoom;
                            chbeagleviewmaxzoom = savedData[0].metadata.eagleview_max_zoom;
                        }

                        chblayer = true

                    }
                }
            }
        zoomBounds = L.latLngBounds([]);
        map.fireEvent('click');
        for (t = 0; t < savedData.length; t++) {
			boo = L.geoJSON(savedData[t].features);
			var b = boo.getBounds();
			if (b.isValid() && savedData[t].metadata.vectorgroup) {
				var area = turf.area(L.rectangle(b).toGeoJSON(15))
				savedData[t].area = area
			} else {
				savedData[t].area = 0;
			}
		}

		savedData.sort(function (a, b) {
			return parseFloat(b.area) - parseFloat(a.area);
		});
		for (var t = 0; t < savedData.length; t++) {
			savedData[t].index = (400 + t);
		}
        for (var z = 0; z < savedData.length; z++) {
            var parsedName = savedData[z].metadata.shortname;
            parsedName = parsedName.replace(/['"]+/g, '');
            parsedName = parsedName.replace(/\s+/g, '');
            parsedName = parsedName.replace(/["]/g, "-dq-");
            parsedName = parsedName.replace(/[']/g, "-sq-");
            parsedName = parsedName.replace(/[>]/g, "-gt-");
            parsedName = parsedName.replace(/[<]/g, "-lt-");
            parsedName = parsedName.replace(/[/]/g, "-bs-");
            parsedName = parsedName.replace(/[(]/g, "-bst-");
            parsedName = parsedName.replace(/[)]/g, "-bse-");
            parsedName = parsedName.replace(/[\\]/g, "-fs-");
            parsedName = parsedName.replace(/[&]/g, "-and-");
            parsedName = parsedName.replace(/[,]/g, "-comma-");
            parsedName = parsedName.replace(/[=]/g, "-equal-");
            parsedName = parsedName.replace(/[%]/g, "-percent-");
            parsedName = parsedName.replace(/[+]/g, "-plus-");
            if (presets[parsedName] === undefined) {
                //console.log(savedData[z].metadata.shortname)
                if (typeof savedData[z].metadata.category !== 'undefined')
                    if(savedData[z].metadata.category != null)
                        category = savedData[z].metadata.category;
                    else
                        category = "General";
                else
                    category = "General";
                // Fix for import issues of beta versions of V2
                if (category == "map" || category == "Map")
                    category = "General";

                presets[parsedName] = {
                    "category": category,
                    "color": savedData[z].metadata.color,
                    "shortname": parsedName,
                    "name": savedData[z].metadata.name,
                    "stroke": true,
                    "fill": true,
                    //"weight": savedData[z].metadata.weight,
                    "weight": 1,
                    "opacity": savedData[z].metadata.opacity,
                    "fillColor": savedData[z].metadata.fillColor,
                    "fillOpacity": savedData[z].metadata.fillOpacity,
                    "groupType": "Map",
                    "dashArray": savedData[z].metadata.dashArray,
                    "dashOffset": savedData[z].metadata.dashOffset,
                    "groupVisible": savedData[z].metadata.groupVisible,
                    "labelVisible": savedData[z].metadata.labelVisible,
                    "legendVisible": savedData[z].metadata.legendVisible,
                    "leafletlayer": isMobile ? L.featureGroup() : L.featureGroup({
                        pane: parsedName
                    })
                };
                if (typeof savedData[z].metadata.vectorgroup !== 'undefined')
                    presets[parsedName].vectorgroup = true;
                else if (typeof savedData[z].metadata.photogroup !== 'undefined')
                    presets[parsedName].photogroup = true;
                else if (typeof savedData[z].metadata.markergroup !== 'undefined')
                    presets[parsedName].markergroup = true;
                else
                    presets[parsedName].vectorgroup = true;
                if (savedData[z].metadata.vectorgroup)
                    if (presets[parsedName].fillColor == null || presets[parsedName].fillColor == "") {
                        presets[parsedName].fillColor = presets[parsedName].color;
                    }
                var feature = presets[parsedName].leafletlayer.feature = presets[parsedName].leafletlayer.feature ||
                    {};
                feature.type = "FeatureGroup";
                if (typeof savedData[z].metadata.vectorgroup !== 'undefined') {
                    if (typeof grps[category] == 'undefined') {
                        grps[category] = [];

                    }

                    var temp = {
                        name: savedData[z].metadata.name,
                        value: parsedName
                    };
                    grps[category].push(temp);

                }

                feature.properties = feature.properties || {};
                presets[parsedName].leafletlayer.on('layeradd', updateGroups);
                presets[parsedName].leafletlayer.on('layerremove', updateGroups);
                copyMetadataToFeatureLayer(presets[parsedName].leafletlayer, parsedName);
                drawnGroups.addLayer(presets[parsedName].leafletlayer);

            } else {
                presets[parsedName].leafletlayer.metadata = savedData[z].metadata;
                presets[parsedName]['legendVisible'] = typeof savedData[z].metadata['legendVisible'] == 'undefined' ? false : savedData[z].metadata['legendVisible'];
                presets[parsedName]['groupVisible'] = typeof savedData[z].metadata['groupVisible'] == 'undefined' ? false : savedData[z].metadata['groupVisible'];
                copyMetadataToFeatureLayer(presets[parsedName].leafletlayer, parsedName);
            }

            for (j = 0; j < savedData[z].features.length; j++) {
                if (savedData[z].features[j].properties !== undefined && savedData[z].features[j].properties !=
                    undefined) {
                    var featureProps = savedData[z].features[j].properties;
                    var featureGeo = savedData[z].features[j].geometry;
                } else {
                    continue;
                }


                if (featureProps.type == 'Photo') {
                    addPhotoMarkers(featureProps.imageURL, featureProps.description, featureProps.date, "",
                        featureProps.markerURL,
                        featureProps.markerSize,
                        featureGeo.coordinates[1], featureGeo.coordinates[0], "", "", featureProps.buildname);

                    imglistLength = imglist.length;
                    imglist[imglistLength] = [];
                    imglist[imglistLength][1] = featureProps.description;
                    imglist[imglistLength][2] = featureProps.date;
                    imglist[imglistLength][3] = featureProps.imageURL;
                    imglist[imglistLength][4] = featureProps.buildname;

                    continue;
                }

                if (featureProps.type == "Marker") {
                    if (!featureProps.markerURL)
                        featureProps.markerURL = myBaseURL+"/images/marker-orange.png";
                    if (!featureProps.markerSize)
                        featureProps.markerSize = [26, 41];

                    var marker = new L.Marker([featureGeo.coordinates[1], featureGeo.coordinates[0]], {
                        icon: new LeafletIcon({
                            iconUrl: featureProps.markerURL,
                            iconSize: featureProps.markerSize
                        })
                    });
                    marker.feature = marker.feature || {};
                    marker.feature.type = "Feature";
                    marker.feature.properties = marker.feature.properties || {};
                    marker.feature.properties = featureProps;
                    var markerlayer = marker;
                    presets[parsedName].leafletlayer.addLayer(markerlayer);
                    markerlayer.on('click', onPolyClick);
                    continue;
                }
                if (featureProps.type == "Text") {
                    addTextArea(featureProps.description, [featureGeo.coordinates[1], featureGeo.coordinates[0]],
                        featureProps.fontSize, featureProps);
                    continue;
                }

                if (featureProps.type == 'Circle') {
                    var circle = new L.Circle([featureGeo.coordinates[1], featureGeo.coordinates[0]], {
                        radius: featureProps.radius
                    });
                    circle.feature = circle.feature || {};
                    circle.feature.type = "Feature";
                    circle.feature.properties = circle.feature.properties || {};
                    circle.feature.properties = featureProps;
                    let mylayer2 = circle;

                    mylayer2.setStyle(presets[parsedName].leafletlayer.metadata);
                    presets[parsedName].leafletlayer.addLayer(mylayer2);
                    mylayer2.on('click', onPolyClick);
                    mylayer2.on('mouseover', function (e) {
                        setHighlight(mylayer2);
                        if ($('#Selected').is(":visible"))
                            unsetHighlight(mylayer2);
                    });
                    mylayer2.on('mouseout', function (e) {
                        unsetHighlight(mylayer2);
                    });
                    continue;
                }
                let glayer;
                if(isMobile) {
                    glayer = L.geoJSON(savedData[z].features[j]);
                } else {
                map.createPane('pane' + z + "_" + j);
                map.getPane('pane' + z + "_" + j).style.zIndex = savedData[z].index;
                 glayer = L.geoJSON(savedData[z].features[j], {
                    pane: 'pane' + z + "_" + j
                });
            }


                glayer.eachLayer(function (clayer) {
                    var feature = clayer.feature = clayer.feature || {};
                    feature.type = "Feature";
                    feature.properties = feature.properties || {};
                    feature.properties = savedData[z].features[j].properties;



                    if (feature.properties.type == "Line") {
                        var shapeOptions = _.omit(presets[parsedName].leafletlayer
                                .metadata,
                            ['fill', 'fillColor',
                                'fillOpacity'
                            ]);
                        clayer.setStyle(shapeOptions);
                        if (clayer.feature.properties.anim == "true")
                            addPolylineAnimation(clayer, true);
                        if (clayer.feature.properties.arrow == "right")
                            addArrowhead(clayer, true);
                    } else {

                        clayer.setStyle(presets[parsedName].leafletlayer.metadata);

                    }
                    presets[parsedName].leafletlayer.addLayer(clayer);
                    clayer.on('mouseover', function (e) {
                        setHighlight(clayer);
                        if ($('#Selected').is(":visible"))
                            unsetHighlight(clayer);
                    });
                    clayer.on('mouseout', function (e) {
                        unsetHighlight(clayer);
                    });
                    clayer.bringToBack();

                    clayer.on('click', onPolyClick);
                });
            }

        }

        setTimeout(async function () {
            myzoomBounds = drawnGroups.getBounds();

            if (myzoomBounds.isValid()) {

                map.fitBounds(myzoomBounds);



            } else {
                var maxBounds = L.latLngBounds(
                    L.latLng(24.0, -128.23), //Southwest
                    L.latLng(50.09, -59.14) //Northeast
                );
                map.fitBounds(maxBounds);
              //  map.setZoom(4);
            }
            if (chblayer == true) {
                 if (chblayertype == "highres" &&chblayerprovider === 'eagleview') {
                    await this.loadEagleView(chbeagleviewlayerid, chbeagleviewpurchaseid, chbeagleviewtilematrixset, chbeagleviewminzoom, chbeagleviewmaxzoom);
                }
				else if (chblayertype == "highres" && !isMobile) {
                    let cacheResponse;
                    let img;
                    console.log('chblayerimage')
                    console.log(chblayerimage)
                    if(chblayerimage) {
                        cacheResponse = await this.getCachedImageryByUrl(chblayerimage);
                        img = _.find(cacheResponse, { 'tiles_url': chblayerimage });
                    }
                    else {
                        cacheResponse = await this.getCachedImagery();
                        cacheResponse.filter(element => {
                            const acc = element.tiles_accesscode.split(',')
                            if (acc.includes(accessCode)) 
                                return element;
                        });
                        cacheResponse = _.sortBy(cacheResponse, { prop: "coverage", desc: true, });
                        img = cacheResponse[0];

                    }
                    
                    if (typeof img != 'undefined') {
                        let downloadResponse = await downloadCached(img.id)
                        if (typeof downloadResponse.error != 'undefined') {
                            if (downloadResponse.error == false) {
                              //  this.switchLayer(newLayerID, oldLayerID);
                                this.displayImagery(downloadResponse)
                            }
                        }
                    }
				} 

			}
        }, 100);

        for (var preset in presets) {
            if (!presets[preset].leafletlayer.metadata.groupVisible) {
                drawnGroups.removeLayer(presets[preset].leafletlayer);
            }
        }
    }
    async function loadEagleView(layerid, purchaseid, tilematrixset, minzoom, maxzoom) {
        const identifier = purchaseid || layerid; // Prefer purchase ID, fallback to layer ID

                    
        const tokenResponse = await this.generateEagleViewToken(identifier, tilematrixset);
        const tileUrl = `/crux/eagleview/tile/${layerid}/{z}/{x}/{y}.jpeg?token=${tokenResponse.token}`;
                        console.log("Creating EagleView tile layer with URL pattern:", tileUrl);

                        const latLngs = tokenResponse.bounds.map(([lng, lat]) => [lat, lng]);


                        const bounds = L.latLngBounds(latLngs);

                       

                        

                        this.overlay = L.tileLayer.maskedOffscreen(tileUrl, {
                            attribution: '&copy; EagleView',
                            minZoom: minzoom,
                            maxZoom: maxzoom,
                            tileBounds: bounds,
                            tileSize: 256
                        }).addTo(map);


    }
    async function generateEagleViewToken(purchaseIdOrLayer, tileMatrixSet) {
            
            const isNumericId = !isNaN(purchaseIdOrLayer) && purchaseIdOrLayer !== null;

            const requestBody = {
                accesscode: vid,
                tileMatrixSet: tileMatrixSet || 'GoogleMapsCompatible_7-22'
            };

            if (isNumericId) {
                // NEW: ID-based approach
                requestBody.purchaseId = purchaseIdOrLayer;
            } else {
                // LEGACY: Layer-based approach
                requestBody.layer = purchaseIdOrLayer;
            }

            const response = await fetch('https://tiles.sitefotos.com/api3/tiles/eagleview/token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody),
            });

            if (!response.ok) {
                throw new Error(`Failed to generate token: ${response.status}`);
            }

            return await response.json();
        }
    async function getCachedImagery () {
            let area = turf.area(L.rectangle(drawnGroups.getBounds()).toGeoJSON(15))
            let paddingarea = ((area + 400) / area) - 1;

            let boundsDG = drawnGroups.getBounds().pad(paddingarea);
            let myurl = "https://tiles.sitefotos.com/api2/tiles/searchcached?accesscode=$accesscode&bounds=$bounds";
            myurl = myurl.replace('$accesscode', accessCode);

            myurl = myurl.replace('$bounds', boundsDG.toBBoxString());
            let data = await (await fetch(myurl)).json();

            console.log(data);
            if (data.length > 0) {
                data = _.uniq(data, 'id');
                data = _.sortBy(data, 'date').reverse();
            }
            for (var i = 0; i < data.length; i++) {
                data[i].displayDate = moment(data[i].date.substring(0,
                    data[i].date.indexOf('T'))).format("MM/DD/YY");
                data[i].displayCoverage = ((data[i].coverage) *
                    100).toFixed(2) + '%'
            }
            return data;
    }
    async function getCachedImageryByUrl (url) {
            let area = turf.area(L.rectangle(drawnGroups.getBounds()).toGeoJSON(15))
            let paddingarea = ((area + 400) / area) - 1;

            let boundsDG = drawnGroups.getBounds().pad(paddingarea);
            let myurl = "https://tiles.sitefotos.com/api2/tiles/searchcachedbyurl?url=$url";
            myurl = myurl.replace('$url', url);

            let data = await (await fetch(myurl)).json();

            console.log(data);
            if (data.length > 0) {
                data = _.uniq(data, 'id');
                data = _.sortBy(data, 'date').reverse();
            }
            for (var i = 0; i < data.length; i++) {
                data[i].displayDate = moment(data[i].date.substring(0,
                    data[i].date.indexOf('T'))).format("MM/DD/YY");
                data[i].displayCoverage = ((data[i].coverage) *
                    100).toFixed(2) + '%'
            }
            return data;
    }

    async function downloadCached(tileid) {

        let myurl = "https://tiles.sitefotos.com/api2/tiles/getcachedtile?accesscode=$accesscode&worker=$worker&tileid=$tileid&bid=$bid&mid=$mid";

        myurl = myurl.replace('$accesscode', accessCode);
        myurl = myurl.replace('$tileid', tileid);
        myurl = myurl.replace('$worker', 0);
        myurl = myurl.replace('$bid', buildingID);
        myurl = myurl.replace('$mid', mapID);
        return await (await fetch(myurl)).json();


    }

    function displayImagery(downloadResponse) {
            let imageUrl = downloadResponse.url;
            if (typeof downloadResponse.bounds == 'string')
                var imageBounds = L.geoJSON(JSON.parse(downloadResponse.bounds))
                    .getBounds();
            else
                var imageBounds = L.geoJSON(downloadResponse.bounds).getBounds();

            
            let overlay = L.imageOverlay(imageUrl, imageBounds)
            overlay.addTo(map);

    }
    
    function importv1Format(savedData) {


		zoomBounds = L.latLngBounds([]);
		map.fireEvent('click');
		if (savedData.hasOwnProperty('Location')) {

			if (savedData.Location.hasOwnProperty('Address1'))
				address1 = savedData.Location.Address1;
			if (savedData.Location.hasOwnProperty('Address2'))
				address2 = savedData.Location.Address2;
			if (savedData.Location.hasOwnProperty('City'))
				city = savedData.Location.City;
			if (savedData.Location.hasOwnProperty('State'))
				state = savedData.Location.State;
			if (savedData.Location.hasOwnProperty('Zip'))
				zip = savedData.Location.Zipcode;
			if (savedData.Location.hasOwnProperty('Country'))
				country = savedData.Location.Country;
		}
		var geoJSONGrouped = L.geoJSON(savedData, {
			filter: function (feature, layer) {
				if (feature.properties == undefined)
					return false;
				if (feature.properties.preset === undefined) {
					return false;
				} else {
					var g_name = feature.properties.preset.toLowerCase();
					var full_name = feature.properties.preset;

					g_name = g_name.replace(/\s+/g, '');
					g_name = g_name.replace(/["]/g, "-dq-");
					g_name = g_name.replace(/[']/g, "-sq-");
					g_name = g_name.replace(/[>]/g, "-gt-");
					g_name = g_name.replace(/[<]/g, "-lt-");
					g_name = g_name.replace(/[/]/g, "-bs-");
					g_name = g_name.replace(/[(]/g, "-bst-");
					g_name = g_name.replace(/[)]/g, "-bse-");
					g_name = g_name.replace(/[\\]/g, "-fs-");
					g_name = g_name.replace(/[&]/g, "-and-");
					g_name = g_name.replace(/[,]/g, "-comma-");
                    g_name = g_name.replace(/[=]/g, "-equal-");
                    g_name = g_name.replace(/[%]/g, "-percent-");
                    g_name = g_name.replace(/[+]/g, "-plus-");
                    g_name = g_name.replace(/[?]/g, "-question-");
					feature.properties.preset = g_name;
					if (presets.hasOwnProperty(g_name)) {
						return true;
					} else {
						parsedName = g_name;

						if (typeof feature.properties.category !== 'undefined')
							category = feature.properties.category;
						else
							category = "General";
						// Fix for import issues of beta versions of V2
						if (category == "map" || category == "Map")
							category = "General";
						presets[parsedName] = {
							"category": "General",
							"color": feature.properties.color,
							"shortname": parsedName,
							"name": full_name,
							"stroke": true,
							"fill": true,
							//"weight": feature.properties.strokeWeight,
							//"opacity": feature.properties.strokeOpacity,
							"weight": 1,
							"opacity": 0.8,
							"fillColor": feature.properties.fillColor,
							"fillOpacity": feature.properties.fillOpacity,
							"subGroups": feature.properties.subGroups,
							"groupType": "Map",
							"dashArray": feature.properties.dashArray,
							"dashOffset": feature.properties.dashOffset,
							"groupVisible": true,

							"labelVisible": false,
							"legendVisible": false,
							"leafletlayer": L.featureGroup()
						};

						if (typeof feature.properties.perimeter !== 'undefined' || typeof feature.properties
							.area !== undefined) {
							presets[parsedName].vectorgroup = true;
						}
						if (presets[parsedName].fillColor == null || presets[parsedName].fillColor == "") {
							presets[parsedName].fillColor = presets[parsedName].color;
						}
						if (typeof presets[parsedName].vectorgroup !== 'undefined') {
							if (typeof grps[category] == 'undefined') {
								grps[category] = [];

							}

							var temp = {
								name: full_name,
								value: parsedName
							};
							grps[category].push(temp);
						}
						var feature = presets[parsedName].leafletlayer.feature = presets[parsedName]
							.leafletlayer
							.feature || {};
						feature.type = "FeatureGroup";
						feature.properties = feature.properties || {};
						presets[parsedName].leafletlayer.on('layeradd', updateGroups);
						presets[parsedName].leafletlayer.on('layerremove', updateGroups);
						copyMetadataToFeatureLayer(presets[parsedName].leafletlayer, parsedName);
						drawnGroups.addLayer(presets[parsedName].leafletlayer);
						return true;
					}
				}
			}
		});
		var geoJSONUnGrouped = L.geoJSON(savedData, {
			filter: function (feature, layer) {
				if (feature.properties == undefined)
					return true;
				if (feature.properties.preset === undefined) {
					return true;
				} else {
					var g_name = feature.properties.preset.toLowerCase();
					var full_name = feature.properties.preset;

					g_name = g_name.replace(/\s+/g, '');
					g_name = g_name.replace(/["]/g, "-dq-");
					g_name = g_name.replace(/[']/g, "-sq-");
					g_name = g_name.replace(/[>]/g, "-gt-");
					g_name = g_name.replace(/[<]/g, "-lt-");
					g_name = g_name.replace(/[/]/g, "-bs-");
					g_name = g_name.replace(/[(]/g, "-bst-");
					g_name = g_name.replace(/[)]/g, "-bse-");
					g_name = g_name.replace(/[\\]/g, "-fs-");
					g_name = g_name.replace(/[&]/g, "-and-");
					g_name = g_name.replace(/[,]/g, "-comma-");
                    g_name = g_name.replace(/[=]/g, "-equal-");
                    g_name = g_name.replace(/[%]/g, "-percent-");
                    g_name = g_name.replace(/[+]/g, "-plus-");
                    g_name = g_name.replace(/[?]/g, "-question-");
					parsedName = g_name;
					feature.properties.preset = g_name
					if (presets.hasOwnProperty(parsedName)) {
						return false;
					} else {
						return true;
					}
				}
			}
		});
		geoJSONGrouped.eachLayer(function (layer) {

			var g_name = layer.feature.properties.preset.toLowerCase();

			g_name = g_name.replace(/\s+/g, '');
			g_name = g_name.replace(/["]/g, "-dq-");
			g_name = g_name.replace(/[']/g, "-sq-");
			g_name = g_name.replace(/[>]/g, "-gt-");
			g_name = g_name.replace(/[<]/g, "-lt-");
			g_name = g_name.replace(/[/]/g, "-bs-");
			g_name = g_name.replace(/[(]/g, "-bst-");
			g_name = g_name.replace(/[)]/g, "-bse-");
			g_name = g_name.replace(/[\\]/g, "-fs-");
			g_name = g_name.replace(/[&]/g, "-and-");
			g_name = g_name.replace(/[,]/g, "-comma-");
            g_name = g_name.replace(/[=]/g, "-equal-");
            g_name = g_name.replace(/[%]/g, "-percent-");
            g_name = g_name.replace(/[+]/g, "-plus-");
            g_name = g_name.replace(/[?]/g, "-question-");
			parsedName = g_name;

			if (layer.feature.properties.anim == "true")
				addPolylineAnimation(layer, true);
			if (layer.feature.properties.arrow == "right")
				addArrowhead(layer, true);
			if (layer.feature.properties.type == "Line") {
				var shapeOptions = _.omit(presets[parsedName].leafletlayer.metadata, ['fill', 'fillColor',
					'fillOpacity'
				]);
				layer.setStyle(shapeOptions);
				if (layer.feature.properties.perimeter === undefined) {
					latlngs = layer._defaultShape ? layer._defaultShape() : layer.getLatLngs();
					var distance = 0;
					if (latlngs.length >= 2) {
						for (i = 0; i < latlngs.length - 1; i++) {
							distance += latlngs[i].distanceTo(latlngs[i + 1]);
						}
						distance = distance * 3.28084;
						distance = numeral(_round(distance, 2)).format('0,0');
					}
					layer.feature.properties.perimeter = distance + ' ft';
				}
			} else if (layer.feature.properties.type == "Circle") {
				var circle = new L.Circle([layer._latlng.lat, layer._latlng.lng], {
					radius: layer.feature.properties.radius
				});

				circle.feature = circle.feature || {};
				circle.feature.type = "Feature";
				circle.feature.properties = circle.feature.properties || {};
				circle.feature.properties = layer.feature.properties

				var layer = circle;

				layer.setStyle(presets[parsedName].leafletlayer.metadata);
				if (layer.feature.properties.radius === undefined || layer.feature.properties.area ===
					undefined || layer.feature
					.properties.perimeter == undefined) {
					var center = layer.getLatLng(),
						rad = layer.getRadius();
					radius = rad * 3.28084;
					area = radius * radius * Math.PI;
					distance = (radius + radius) * Math.PI;
					distance = numeral(_round(distance, 2)).format('0,0');
					subtractThis = getSubtractAreas(layer.feature.properties.name);
					area = area - subtractThis;
					area = numeral(Math.round(area)).format('0,0');
					layer.feature.properties.radius = rad;
					layer.feature.properties.area = area + ' sqft';
					layer.feature.properties.perimeter = distance + ' ft';
				}
			} else if (layer.feature.properties.type == "Polygon") {
				if (layer.feature.properties.area === undefined || layer.feature.properties.perimeter ==
					undefined) {
					latlngs = layer._defaultShape ? layer._defaultShape() : layer.getLatLngs();
					distance = 0;
					area = Math.round(L.GeometryUtil.geodesicArea(latlngs) *
						10.76391041671);
					subtractThis = getSubtractAreas(layer.feature.properties.name);
					area = area - subtractThis;
					area = numeral(area).format('0,0');
					layer.feature.properties.area = area + ' sqft';
					if (latlngs.length > 2) {
						for (i = 0; i < latlngs.length - 1; i++) {
							distance += latlngs[i].distanceTo(latlngs[i + 1]);
						}
						distance += latlngs[latlngs.length - 1].distanceTo(latlngs[0]);
						distance = distance * 3.28084;
						distance = numeral(_round(distance, 2)).format('0,0');
					}
					layer.feature.properties.perimeter = distance + ' ft';
				}
				layer.setStyle(presets[parsedName].leafletlayer.metadata);
			} else if (layer.feature.properties.type == "Rectangle") {
				if (layer.feature.properties.area === undefined || layer.feature.properties.perimeter ==
					undefined) {
					var latlngs = layer._defaultShape ? layer._defaultShape() : layer.getLatLngs(),
						distance = 0,
						area = Math.round(L.GeometryUtil.geodesicArea(latlngs) *
							10.76391041671);

					var subtractThis = getSubtractAreas(layer.feature.properties.name);
					area = area - subtractThis;
					area = numeral(area).format('0,0');
					layer.feature.properties.area = area + ' sqft';
					if (latlngs.length > 2) {
						for (var i = 0; i < latlngs.length - 1; i++) {
							distance += latlngs[i].distanceTo(latlngs[i + 1]);
						}
						distance += latlngs[latlngs.length - 1].distanceTo(latlngs[0]);
						distance = distance * 3.28084;
						distance = numeral(_round(distance, 2)).format('0,0');
					}
					layer.feature.properties.perimeter = distance + ' ft';
				}
				layer.setStyle(presets[parsedName].leafletlayer.metadata);
			} else if (layer.feature.properties.type == "Point") {
				if (!layer.feature.properties.markerURL)
					layer.feature.properties.markerURL = "/images/marker-orange.png";
				if (!layer.feature.properties.markerSize)
					layer.feature.properties.markerSize = [26, 41];
				var marker = new L.Marker([layer._latlng.lat, layer._latlng.lng], {
					icon: new LeafletIcon({
						iconUrl: layer.feature.properties.markerURL,
						iconSize: layer.feature.properties.markerSize
					})
				});
				var feature = marker.feature = marker.feature || {};
				feature.type = "Feature";
				feature.properties = feature.properties || {};
				feature.properties = layer.feature.properties;
				feature.properties.type = "Marker";
				//	preset = addMarkerPreset('orangemarker', 'Orange Marker');
				//	preset.leafletlayer.addLayer(marker);

				layer = marker;

				//layer.on('click', onPolyClick);
			} else {
				layer.setStyle(presets[parsedName].leafletlayer.metadata);
			}

			if (layer.feature.properties.type != "Marker") {
				presets[parsedName].leafletlayer.addLayer(layer);
				layer.on('mouseover', function (e) {
					setHighlight(layer);
					if ($('#Selected').is(":visible"))
						unsetHighlight(layer);
				});
				layer.on('mouseout', function (e) {
					unsetHighlight(layer);
				});
				layer.on('click', onPolyClick);
			}

		});
		geoJSONUnGrouped.eachLayer(function (layer) {
			if (layer.feature.properties === undefined) {
				layer.feature.properties = {};
				layer.feature.properties.type = "Circle";
				layer.feature.properties.radius = 1;
				layer.feature.properties.name = "Circle-" + shapeCounter++;
			}
			if (layer.feature.properties.preset === undefined) {
				layer.feature.properties.preset = 'none';
			}
			layerProps = layer.feature.properties;
			if (layerProps.anim == "true")
				addPolylineAnimation(layer, true);
			if (layerProps.arrow == "right")
				addArrowhead(layer, true);
			if (layerProps.type == "Line" || layerProps.type == "Rectangle" || layerProps
				.type == "Polygon" || layerProps.type == "Circle") {

				if (layerProps.type == "Line") {
					var shapeOptions = _.omit(presets[layerProps.preset].leafletlayer.metadata, ['fill',
						'fillColor', 'fillOpacity'
					]);
					layer.setStyle(shapeOptions);
					if (layer.feature.properties.perimeter === undefined) {
						latlngs = layer._defaultShape ? layer._defaultShape() : layer.getLatLngs();
						var distance = 0;
						if (latlngs.length >= 2) {
							for (i = 0; i < latlngs.length - 1; i++) {
								distance += latlngs[i].distanceTo(latlngs[i + 1]);
							}
							distance = distance * 3.28084;
							distance = numeral(_round(distance, 2)).format('0,0');
						}
						layer.feature.properties.perimeter = distance + ' ft';
					}
				} else if (layer.feature.properties.type == "Circle") {
					var circle = new L.Circle([layer._latlng.lat, layer._latlng.lng], {
						radius: layer.feature.properties.radius
					});
					circle.feature = circle.feature || {};
					circle.feature.type = "Feature";
					circle.feature.properties = circle.feature.properties || {};
					circle.feature.properties = layer.feature.properties
					var layer = circle;

					layer.setStyle(presets['none'].leafletlayer.metadata);
					if (layer.feature.properties.radius === undefined || layer.feature.properties.area ===
						undefined || layer.feature
						.properties.perimeter == undefined) {
						var center = layer.getLatLng(),
							rad = layer.getRadius();
						radius = rad * 3.28084;
						area = radius * radius * Math.PI;
						distance = (radius + radius) * Math.PI;
						distance = numeral(_round(distance, 2)).format('0,0');
						subtractThis = getSubtractAreas(layer.feature.properties.name);
						area = area - subtractThis;
						area = numeral(Math.round(area)).format('0,0');
						layer.feature.properties.radius = rad;
						layer.feature.properties.area = area + ' sqft';
						layer.feature.properties.perimeter = distance + ' ft';
					}
				} else if (layer.feature.properties.type == "Polygon") {
					if (layer.feature.properties.area === undefined || layer.feature.properties.perimeter ==
						undefined) {
						latlngs = layer._defaultShape ? layer._defaultShape() : layer.getLatLngs();
						distance = 0;
						area = Math.round(L.GeometryUtil.geodesicArea(latlngs) *
							10.76391041671);
						subtractThis = getSubtractAreas(layer.feature.properties.name);
						area = area - subtractThis;
						area = numeral(area).format('0,0');
						layer.feature.properties.area = area + ' sqft';
						if (latlngs.length > 2) {
							for (i = 0; i < latlngs.length - 1; i++) {
								distance += latlngs[i].distanceTo(latlngs[i + 1]);
							}
							distance += latlngs[latlngs.length - 1].distanceTo(latlngs[0]);
							distance = distance * 3.28084;
							distance = numeral(_round(distance, 2)).format('0,0');
						}
						layer.feature.properties.perimeter = distance + ' ft';
					}
					layer.setStyle(presets['none'].leafletlayer.metadata);
				} else if (layer.feature.properties.type == "Rectangle") {
					if (layer.feature.properties.area === undefined || layer.feature.properties.perimeter ==
						undefined) {
						var latlngs = layer._defaultShape ? layer._defaultShape() : layer.getLatLngs(),
							distance = 0,
							area = Math.round(L.GeometryUtil.geodesicArea(latlngs) *
								10.76391041671);

						var subtractThis = getSubtractAreas(layer.feature.properties.name);
						area = area - subtractThis;
						area = numeral(area).format('0,0');
						layer.feature.properties.area = area + ' sqft';
						if (latlngs.length > 2) {
							for (var i = 0; i < latlngs.length - 1; i++) {
								distance += latlngs[i].distanceTo(latlngs[i + 1]);
							}
							distance += latlngs[latlngs.length - 1].distanceTo(latlngs[0]);
							distance = distance * 3.28084;
							distance = numeral(_round(distance, 2)).format('0,0');
						}
						layer.feature.properties.perimeter = distance + ' ft';
					}
					layer.setStyle(presets['none'].leafletlayer.metadata);
				} else {
					layer.setStyle(presets['none'].leafletlayer.metadata);
				}
				presets["none"].leafletlayer.addLayer(layer);
				layer.on('mouseover', function (e) {
					setHighlight(layer);
					if ($('#Selected').is(":visible"))
						unsetHighlight(layer);
				});
				layer.on('mouseout', function (e) {
					unsetHighlight(layer);
				});
				layer.on('click', onPolyClick);
			} else if (layerProps.type == "Marker") {
				if (!layerProps.markerURL)
					layerProps.markerURL = "/images/marker-orange.png";
				if (!layerProps.markerSize)
					layerProps.markerSize = [26, 41];
				var marker = new L.Marker([layer._latlng.lat, layer._latlng.lng], {
					icon: new LeafletIcon({
						iconUrl: layerProps.markerURL,
						iconSize: layerProps.markerSize
					})
				});
				var feature = marker.feature = marker.feature || {};
				feature.type = "Feature";
				feature.properties = feature.properties || {};
				feature.properties = layerProps
				presets["markers"].leafletlayer.addLayer(marker);
				layer = marker;
				layer.on('click', onPolyClick);
			} else if (layerProps.type == "Photo") {
				addPhotoMarkers(layerProps.imageURL, layerProps.description, layerProps.date, "", layerProps
					.markerURL,
					layerProps.markerSize,
					layer._latlng.lat, layer._latlng.lng, "", "", layerProps.buildname);
				imglistLength = imglist.length;
				imglist[imglistLength] = [];
				imglist[imglistLength][1] = layerProps.description;
				imglist[imglistLength][2] = layerProps.date;
				imglist[imglistLength][3] = layerProps.imageURL;
				imglist[imglistLength][4] = layerProps.buildname;

			} else if (layerProps.type == 'Text') {

				addTextArea(layerProps.description, layer._latlng, layerProps.fontSize, layerProps);

			}
		});

        setTimeout(function () {
            myzoomBounds = drawnGroups.getBounds();

            if (myzoomBounds.isValid()) {

                map.fitBounds(myzoomBounds);



            } else {
                var maxBounds = L.latLngBounds(
                    L.latLng(24.0, -128.23), //Southwest
                    L.latLng(50.09, -59.14) //Northeast
                );
                map.fitBounds(maxBounds);
               // map.setZoom(4);
            }
        }, 100);
		addPresetsInSelect();

	}
    function importv1FormatOld(savedData) {

        zoomBounds = L.latLngBounds([]);
        map.fireEvent('click');
        if (savedData.hasOwnProperty('Location')) {
            if (savedData.Location.hasOwnProperty('Address1'))
                address1 = savedData.Location.Address1;
            if (savedData.Location.hasOwnProperty('Address2'))
                address2 = savedData.Location.Address2;
            if (savedData.Location.hasOwnProperty('City'))
                city = savedData.Location.City;
            if (savedData.Location.hasOwnProperty('State'))
                state = savedData.Location.State;
            if (savedData.Location.hasOwnProperty('Zip'))
                zip = savedData.Location.Zipcode;
            if (savedData.Location.hasOwnProperty('Country'))
                country = savedData.Location.Country;
        }
        var geoJSONGrouped = L.geoJSON(savedData, {
            filter: function (feature, layer) {
                if (feature.properties == undefined)
                    return false;
                if (feature.properties.preset === undefined) {
                    return false;
                } else {
                    if (presets.hasOwnProperty(feature.properties.preset)) {
                        return true;
                    } else {
                        parsedName = feature.properties.preset;
                        parsedName = parsedName.replace(/['"]+/g, '');
                        presets[parsedName] = {
                            "category": "map",
                            "color": feature.properties.color,
                            "shortname": parsedName,
                            "name": feature.properties.preset,
                            "stroke": true,
                            "fill": true,
							//"weight": feature.properties.strokeWeight,
							//"opacity": feature.properties.strokeOpacity,
							"weight": 0.1,
							"opacity": 0.7,
                            "fillColor": feature.properties.fillColor,
                            "fillOpacity": feature.properties.fillOpacity,
                            "subGroups": feature.properties.subGroups,
                            "groupType": feature.properties.groupType,
                            "dashArray": feature.properties.dashArray,
                            "dashOffset": feature.properties.dashOffset,
                            "groupVisible": true,
                            "labelVisible": false,
                            "legendVisible": false,
                            "leafletlayer": L.featureGroup()
                        };
                        if (presets[parsedName].fillColor == null || presets[parsedName].fillColor ==
                            "") {
                            presets[parsedName].fillColor = presets[parsedName].color;
                        }
                        var feature = presets[parsedName].leafletlayer.feature = presets[parsedName].leafletlayer
                            .feature || {};
                        feature.type = "FeatureGroup";
                        feature.properties = feature.properties || {};
                        presets[parsedName].leafletlayer.on('layeradd', updateGroups);
                        presets[parsedName].leafletlayer.on('layerremove', updateGroups);
                        copyMetadataToFeatureLayer(presets[parsedName].leafletlayer, parsedName);
                        drawnGroups.addLayer(presets[parsedName].leafletlayer);
                        return true;
                    }
                }
            }
        });
        var geoJSONUnGrouped = L.geoJSON(savedData, {
            filter: function (feature, layer) {
                if (feature.properties == undefined)
                    return true;
                if (feature.properties.preset === undefined) {
                    return true;
                } else {
                    parsedName = feature.properties.preset;
                    parsedName = parsedName.replace(/['"]+/g, '');
                    if (presets.hasOwnProperty(parsedName)) {
                        return false;
                    } else {
                        return true;
                    }
                }
            }
        });
        geoJSONGrouped.eachLayer(function (layer) {
            parsedName = layer.feature.properties.preset;
            parsedName = parsedName.replace(/['"]+/g, '');

            if (layer.feature.properties.anim == "true")
                addPolylineAnimation(layer, true);
            if (layer.feature.properties.arrow == "right")
                addArrowhead(layer, true);
            if (layer.feature.properties.type == "Line") {
                var shapeOptions = _.omit(presets[parsedName].leafletlayer.metadata, ['fill', 'fillColor',
                    'fillOpacity'
                ]);
                layer.setStyle(shapeOptions);
                if (layer.feature.properties.perimeter === undefined) {
                    latlngs = layer._defaultShape ? layer._defaultShape() : layer.getLatLngs();
                    var distance = 0;
                    if (latlngs.length >= 2) {
                        for (i = 0; i < latlngs.length - 1; i++) {
                            distance += latlngs[i].distanceTo(latlngs[i + 1]);
                        }
                        distance = distance * 3.28084;
                        distance = numeral(_round(distance, 2)).format('0,0');
                    }
                    layer.feature.properties.perimeter = distance + ' ft';
                }
            } else if (layer.feature.properties.type == "Circle") {
                var circle = new L.Circle([layer._latlng.lat, layer._latlng.lng], {
                    radius: layer.feature.properties.radius
                });

                circle.feature = circle.feature || {};
                circle.feature.type = "Feature";
                circle.feature.properties = circle.feature.properties || {};
                circle.feature.properties = layer.feature.properties

                var layer = circle;

                layer.setStyle(presets[parsedName].leafletlayer.metadata);
                if (layer.feature.properties.radius === undefined || layer.feature.properties.area ===
                    undefined || layer.feature
                    .properties.perimeter == undefined) {
                    var center = layer.getLatLng(),
                        rad = layer.getRadius();
                    radius = rad * 3.28084;
                    area = radius * radius * Math.PI;
                    distance = (radius + radius) * Math.PI;
                    distance = numeral(_round(distance, 2)).format('0,0');
                    subtractThis = getSubtractAreas(layer.feature.properties.name);
                    area = area - subtractThis;
                    area = numeral(Math.round(area)).format('0,0');
                    layer.feature.properties.radius = rad;
                    layer.feature.properties.area = area + ' sqft';
                    layer.feature.properties.perimeter = distance + ' ft';
                }
            } else if (layer.feature.properties.type == "Polygon") {
                if (layer.feature.properties.area === undefined || layer.feature.properties.perimeter ==
                    undefined) {
                    latlngs = layer._defaultShape ? layer._defaultShape() : layer.getLatLngs();
                    distance = 0;
                    area = Math.round(L.GeometryUtil.geodesicArea(latlngs) *
                        10.76391041671);
                    subtractThis = getSubtractAreas(layer.feature.properties.name);
                    area = area - subtractThis;
                    area = numeral(area).format('0,0');
                    layer.feature.properties.area = area + ' sqft';
                    if (latlngs.length > 2) {
                        for (i = 0; i < latlngs.length - 1; i++) {
                            distance += latlngs[i].distanceTo(latlngs[i + 1]);
                        }
                        distance += latlngs[latlngs.length - 1].distanceTo(latlngs[0]);
                        distance = distance * 3.28084;
                        distance = numeral(_round(distance, 2)).format('0,0');
                    }
                    layer.feature.properties.perimeter = distance + ' ft';
                }
                layer.setStyle(presets[parsedName].leafletlayer.metadata);
            } else if (layer.feature.properties.type == "Rectangle") {
                if (layer.feature.properties.area === undefined || layer.feature.properties.perimeter ==
                    undefined) {
                    var latlngs = layer._defaultShape ? layer._defaultShape() : layer.getLatLngs(),
                        distance = 0,
                        area = Math.round(L.GeometryUtil.geodesicArea(latlngs) *
                            10.76391041671);

                    var subtractThis = getSubtractAreas(layer.feature.properties.name);
                    area = area - subtractThis;
                    area = numeral(area).format('0,0');
                    layer.feature.properties.area = area + ' sqft';
                    if (latlngs.length > 2) {
                        for (var i = 0; i < latlngs.length - 1; i++) {
                            distance += latlngs[i].distanceTo(latlngs[i + 1]);
                        }
                        distance += latlngs[latlngs.length - 1].distanceTo(latlngs[0]);
                        distance = distance * 3.28084;
                        distance = numeral(_round(distance, 2)).format('0,0');
                    }
                    layer.feature.properties.perimeter = distance + ' ft';
                }
                layer.setStyle(presets[parsedName].leafletlayer.metadata);
            } else {
                layer.setStyle(presets[parsedName].leafletlayer.metadata);
            }
            presets[parsedName].leafletlayer.addLayer(layer);
            layer.on('mouseover', function (e) {
                setHighlight(layer);
                if ($('#Selected').is(":visible"))
                    unsetHighlight(layer);
            });
            layer.on('mouseout', function (e) {
                unsetHighlight(layer);
            });
            layer.on('click', onPolyClick);
        });
        geoJSONUnGrouped.eachLayer(function (layer) {
            if (layer.feature.properties === undefined) {
                layer.feature.properties = {};
                layer.feature.properties.type = "Circle";
                layer.feature.properties.radius = 1;
                layer.feature.properties.name = "Circle-" + shapeCounter++;
            }
            if (layer.feature.properties.preset === undefined) {
                layer.feature.properties.preset = 'none';
            }
            layerProps = layer.feature.properties;
            if (layerProps.anim == "true")
                addPolylineAnimation(layer, true);
            if (layerProps.arrow == "right")
                addArrowhead(layer, true);
            if (layerProps.type == "Line" || layerProps.type == "Rectangle" || layerProps
                .type == "Polygon" || layerProps.type == "Circle") {

                if (layerProps.type == "Line") {
                    var shapeOptions = _.omit(presets[layerProps.preset].leafletlayer.metadata, ['fill',
                        'fillColor', 'fillOpacity'
                    ]);
                    layer.setStyle(shapeOptions);
                    if (layer.feature.properties.perimeter === undefined) {
                        latlngs = layer._defaultShape ? layer._defaultShape() : layer.getLatLngs();
                        var distance = 0;
                        if (latlngs.length >= 2) {
                            for (i = 0; i < latlngs.length - 1; i++) {
                                distance += latlngs[i].distanceTo(latlngs[i + 1]);
                            }
                            distance = distance * 3.28084;
                            distance = numeral(_round(distance, 2)).format('0,0');
                        }
                        layer.feature.properties.perimeter = distance + ' ft';
                    }
                } else if (layer.feature.properties.type == "Circle") {
                    var circle = new L.Circle([layer._latlng.lat, layer._latlng.lng], {
                        radius: layer.feature.properties.radius
                    });
                    circle.feature = circle.feature || {};
                    circle.feature.type = "Feature";
                    circle.feature.properties = circle.feature.properties || {};
                    circle.feature.properties = layer.feature.properties
                    var layer = circle;

                    layer.setStyle(presets['none'].leafletlayer.metadata);
                    if (layer.feature.properties.radius === undefined || layer.feature.properties.area ===
                        undefined || layer.feature
                        .properties.perimeter == undefined) {
                        var center = layer.getLatLng(),
                            rad = layer.getRadius();
                        radius = rad * 3.28084;
                        area = radius * radius * Math.PI;
                        distance = (radius + radius) * Math.PI;
                        distance = numeral(_round(distance, 2)).format('0,0');
                        subtractThis = getSubtractAreas(layer.feature.properties.name);
                        area = area - subtractThis;
                        area = numeral(Math.round(area)).format('0,0');
                        layer.feature.properties.radius = rad;
                        layer.feature.properties.area = area + ' sqft';
                        layer.feature.properties.perimeter = distance + ' ft';
                    }
                } else if (layer.feature.properties.type == "Polygon") {
                    if (layer.feature.properties.area === undefined || layer.feature.properties.perimeter ==
                        undefined) {
                        latlngs = layer._defaultShape ? layer._defaultShape() : layer.getLatLngs();
                        distance = 0;
                        area = Math.round(L.GeometryUtil.geodesicArea(latlngs) *
                            10.76391041671);
                        subtractThis = getSubtractAreas(layer.feature.properties.name);
                        area = area - subtractThis;
                        area = numeral(area).format('0,0');
                        layer.feature.properties.area = area + ' sqft';
                        if (latlngs.length > 2) {
                            for (i = 0; i < latlngs.length - 1; i++) {
                                distance += latlngs[i].distanceTo(latlngs[i + 1]);
                            }
                            distance += latlngs[latlngs.length - 1].distanceTo(latlngs[0]);
                            distance = distance * 3.28084;
                            distance = numeral(_round(distance, 2)).format('0,0');
                        }
                        layer.feature.properties.perimeter = distance + ' ft';
                    }
                    layer.setStyle(presets['none'].leafletlayer.metadata);
                } else if (layer.feature.properties.type == "Rectangle") {
                    if (layer.feature.properties.area === undefined || layer.feature.properties.perimeter ==
                        undefined) {
                        var latlngs = layer._defaultShape ? layer._defaultShape() : layer.getLatLngs(),
                            distance = 0,
                            area = Math.round(L.GeometryUtil.geodesicArea(latlngs) *
                                10.76391041671);

                        var subtractThis = getSubtractAreas(layer.feature.properties.name);
                        area = area - subtractThis;
                        area = numeral(area).format('0,0');
                        layer.feature.properties.area = area + ' sqft';
                        if (latlngs.length > 2) {
                            for (var i = 0; i < latlngs.length - 1; i++) {
                                distance += latlngs[i].distanceTo(latlngs[i + 1]);
                            }
                            distance += latlngs[latlngs.length - 1].distanceTo(latlngs[0]);
                            distance = distance * 3.28084;
                            distance = numeral(_round(distance, 2)).format('0,0');
                        }
                        layer.feature.properties.perimeter = distance + ' ft';
                    }
                    layer.setStyle(presets['none'].leafletlayer.metadata);
                } else {
                    layer.setStyle(presets['none'].leafletlayer.metadata);
                }
                presets["none"].leafletlayer.addLayer(layer);
                layer.on('mouseover', function (e) {
                    setHighlight(layer);
                    if ($('#Selected').is(":visible"))
                        unsetHighlight(layer);
                });
                layer.on('mouseout', function (e) {
                    unsetHighlight(layer);
                });
                layer.on('click', onPolyClick);
            } else if (layerProps.type == "Marker") {
                if (!layerProps.markerURL)
                    layerProps.markerURL = "/images/marker-orange.png";
                if (!layerProps.markerSize)
                    layerProps.markerSize = [26, 41];
                var marker = new L.Marker([layer._latlng.lat, layer._latlng.lng], {
                    icon: new LeafletIcon({
                        iconUrl: layerProps.markerURL,
                        iconSize: layerProps.markerSize
                    })
                });
                var feature = marker.feature = marker.feature || {};
                feature.type = "Feature";
                feature.properties = feature.properties || {};
                feature.properties = layerProps
                presets["markers"].leafletlayer.addLayer(marker);
                layer = marker;
                layer.on('click', onPolyClick);
            } else if (layerProps.type == "Photo") {
                addPhotoMarkers(layerProps.imageURL, layerProps.description, layerProps.date, "",
                    layerProps.markerURL,
                    layerProps.markerSize,
                    layer._latlng.lat, layer._latlng.lng, "", "", layerProps.buildname);
                imglistLength = imglist.length;
                imglist[imglistLength] = [];
                imglist[imglistLength][1] = layerProps.description;
                imglist[imglistLength][2] = layerProps.date;
                imglist[imglistLength][3] = layerProps.imageURL;
                imglist[imglistLength][4] = layerProps.buildname;

            } else if (layerProps.type == 'Text') {

                addTextArea(layerProps.description, layer._latlng, layerProps.fontSize, layerProps );

            }
        });

        setTimeout(function () {
            myzoomBounds = drawnGroups.getBounds();

            if (myzoomBounds.isValid()) {

                map.fitBounds(myzoomBounds);



            } else {
                var maxBounds = L.latLngBounds(
                    L.latLng(24.0, -128.23), //Southwest
                    L.latLng(50.09, -59.14) //Northeast
                );
                map.fitBounds(maxBounds);
              //  map.setZoom(4);
            }
        }, 100);

    }


    $("#cb_animate").bind('change', function () {
        var tempselectedItem = selectedItem;
        if (this.checked) {
            if ($("#cellAnimateIcon").attr('src') == "") {
                $.snackbar({
                    content: "Please select an icon first, by clicking the pencil icon."
                });
                this.checked = false;
            } else {
                selectedItem.feature.properties.anim = "true";
                addPolylineAnimation(selectedItem, true);
            }
        } else {
            selectedItem.feature.properties.anim = "false";
            addPolylineAnimation(selectedItem, false);
        }
    });

    function generateSaveData() {
        var saveData = L.featureGroup();

        for (var preset in presets) {
            saveData.addLayer(presets[preset].leafletlayer);
        }
        return saveData.toGeoJSON().features;
    }
</script>

<style>
 
    .group-actions {
        color: white;
    }

    .group-actions:hover {
        color: rgb(179, 179, 179);
        cursor: pointer;
    }

    .n-row {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        padding-top: 2px;
        padding-bottom: 2px;
    }

    .n-col {
        flex-basis: 100%;
        padding-left: 2px;
        padding-right: 2px;
    }

    .collapse-icons:hover {
        color: rgb(179, 179, 179);
        cursor: pointer;
    }

    @media screen and (min-width: 800px) {
        .n-col {
            flex: 1;
        }
    }

    ._10 {
        flex: 1.0;
    }

    ._15 {
        flex: 1.5;
    }

    ._35 {
        flex: 3.5;
    }

    ._25 {
        flex: 2.5;
    }

    ._30 {
        flex: 3.0;
    }

    ._40 {
        flex: 4.0;
    }

    ._60 {
        flex: 6.0;
    }

    ._80 {
        flex: 8.0;
    }

    ._70 {
        flex: 7.0;
    }

    ._50 {
        flex: 5.0;
    }

    ._20 {
        flex: 2.0;
    }

    ._90 {
        flex: 9.0;
    }

    ._75 {
        flex: 7.5;
    }

    .n-container {

        margin: 0 auto;
        margin-top: 10px;
        margin-left: 12px;

    }

    .item-properties {
        border-color: #337ab7;
        border-top-style: solid;
        padding-top: 10px;

    }

    .input-small {
        height: 24px;
        line-height: 24px; //font-size: 10px;
        padding: 1px 5px;
        border-radius: 3px;
        border-top-color: rgb(237, 237, 237);
        box-shadow: rgba(3, 23, 25, 0.2) 0px 2px 0px 0px;
        background-color: rgb(239, 239, 239);
    }

    .applyButton {
        font-size: 10px;
        padding: 1px 5px;
        background-color: rgb(239, 239, 239);
    }

    .applyButton:hover {
        background-color: rgb(189, 189, 189);
    }

    .sm-button {
        font-size: 14px;
        padding: 1px 5px;
        background-color: rgb(229, 229, 229);
    }

    .sm-button:hover {
        background-color: rgb(189, 189, 189);
    }



    .text-label {
        line-height: 24px; //font-size: 10px;
        font-weight: 600; //font-family: 'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif;
    }

    .text-label-text {
        line-height: 24px; //font-size: 10px;
        font-weight: 400; //font-family: 'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif;
    }


    #propertiesGroupName {
        border-bottom: blue;
        border-bottom-style: dotted;
        border-bottom-width: 1px;
        font-size: 12px;
        font-weight: 600; //font-family: 'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif;
        white-space: nowrap;
        line-height: 20px;
        overflow: hidden;
    }

    #propertiesGroupName:focus,
    #propertiesGroupName:active {
        border: none;
        outline: none;
        border-bottom: blue;
        border-bottom-style: dotted;
        border-bottom-width: 1px;
    }

    #propertiesGroupName br {
        display: none;
    }

    #propertiesGroupName * {
        display: inline;
        white-space: nowrap;
    }

    .switch {
        position: relative;
        display: inline-block;
        width: 36px;
        height: 18px;
    }

    .switch input {
        display: none;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgb(185, 185, 185);
        -webkit-transition: .4s;
        transition: .4s;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 12px;
        width: 12px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        -webkit-transition: .4s;
        transition: .4s;
    }

    input:checked+.slider {
        background-color: #2196F3;
    }

    input:focus+.slider {
        box-shadow: 0 0 1px #2196F3;
    }

    input:checked+.slider:before {
        -webkit-transform: translateX(14px);
        -ms-transform: translateX(14px);
        transform: translateX(14px);
    }

    /* Rounded sliders */

    .slider.round {
        border-radius: 18px;
    }

    .slider.round:before {
        border-radius: 50%;
    }

    .group-properties {
        color: black;
    }

    .leaflet-layer-item {
        border-color: white;
        border: 1px;
        padding-left: 20px;
        font-size: 12px;
        margin-top: 5px;
    }

    .list-group-item.active,
    .list-group-item.active:hover,
    .list-group-item.active:focus {
        z-index: 2;
        color: #000;
    }

    .list-group {
        padding-left: 0;
        margin-bottom: 0px;
    }

    .fa {
        font-size: 1.5em;

    }

    .leaflet-legend-control {
        background: #FFF;
        border: 2px solid #FFF;
        padding: 6px;
        max-height: 250px;
        overflow-y: auto;
        overflow-x: hidden;
        line-height: 1.2;
        max-width: 200px!important;

    }

    .leaflet-legend-control .legend-symbol {
        margin-right: 6px;
        display: inline-flex;
        vertical-align: middle;
    }

    .leaflet-legend-control .legend-label {
        max-width: 200px;
        display: inline-block;
        vertical-align: middle;
        word-wrap: break-word;
        color: #555;
        font-size: 11px;
        font-weight: 400;
    }

    .leaflet-legend-control .legend-block {
        position: relative;
        margin-right: 7px;
        width: 100%;
    }
</style>
