<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>QR Codes for Sites</title>
  <link rel="icon" href="data:,">
    <%- '<script>' %>
    <% for (const [key, value] of Object.entries(data)) { %>
    <% if (typeof value === 'string') { %>
    var <%= key %> = '<%- value %>';
    
    <% continue; } %>
    <% if (typeof value === 'number') { %>
    var <%= key %> = <%= value %>;
    <% continue; } %>
    <% if (typeof value === 'object') { %>
    var <%= key %> = <%- JSON.stringify(value) %>;
    <% continue; } %>
    <% if (typeof value === 'boolean') { %>
    var <%= key %> = <%- value %>;
    <% continue; } %>
    <% } %>
    <%- '</script>' %>

  
  <link href="/backoffice/core/css/tabulator.min.css" rel="stylesheet">
  <script type="text/javascript" src="https://unpkg.com/tabulator-tables@5.5.0/dist/js/tabulator.min.js"></script>
  
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
    }
    h1 {
      text-align: center;
    }
    .grid-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }
    .grid-item {
      text-align: center;
      border: 1px solid #ccc;
      padding: 10px;
      border-radius: 8px;
      background: #f9f9f9;
    }
    .grid-item canvas {
      max-width: 100%;
      height: auto;
    }
    .grid-item h2 {
      margin-top: 0;
    }
    
    /* Tabulator custom styles */
    #sites-table {
      margin-top: 20px;
      margin-bottom: 30px;
    }
    
    .print-btn {
      background-color: #4CAF50;
      border: none;
      color: white;
      padding: 8px 12px;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      font-size: 14px;
      margin: 2px;
      cursor: pointer;
      border-radius: 4px;
    }
    
    /* Print styles */
    @media print {
      body {
        margin: 0;
        padding: 0;
        background: none;
      }
      
      h1 {
        margin-bottom: 15px;
        color: black;
      }

      .grid-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10mm;
        margin: 0;
      }

      .grid-item {
        break-inside: avoid;
        page-break-inside: avoid;
        border: none;
        background: none;
        padding: 5mm;
      }

      .grid-item canvas, 
      .grid-item svg {
        width: 90mm !important;  
        height: 90mm !important;
      }

      .grid-item h2 {
        color: black;
        font-size: 16pt;
        margin-bottom: 5mm;
      }
      
      /* Hide table when printing individual QR code */
      .hide-on-print {
        display: none !important;
      }
      
      /* Center single QR code on page */
      .print-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        width: 100%;
        position: absolute;
        top: 0;
        left: 0;
      }
      
      .print-qr-item {
        text-align: center;
      }
      
      .print-qr-item svg {
        width: 150mm !important;
        height: 150mm !important;
      }
      
      .print-qr-item h2 {
        font-size: 24pt;
        margin-bottom: 10mm;
      }
    }
    
    /* Hidden container for print view */
    #print-view {
      display: none;
    }
    
    /* Print iframe style */
    #print-frame {
      display: none;
    }
  </style>
</head>
<body>
  <h1 class="hide-on-print">Site QR Codes</h1>
  
  <!-- Tabulator table -->
  <div id="sites-table" class="hide-on-print"></div>
  
  <!-- Hidden container for grid view (original) -->
  <div class="grid-container hide-on-print" id="qr-grid"></div>
  
  <!-- Container for print view -->
  <div id="print-view"></div>
  
  <!-- Container for print frames -->
  <div id="print-frames-container"></div>

  <!-- Include the QR Code Styling library from a CDN -->
  <script src="https://unpkg.com/qr-code-styling@1.5.0/lib/qr-code-styling.js"></script>
  <script>
    // Create QR codes for grid view
    const gridContainer = document.getElementById("qr-grid");
    const printView = document.getElementById("print-view");
    const printFramesContainer = document.getElementById("print-frames-container");
    
    // Create Tabulator table
    const table = new Tabulator("#sites-table", {
      data: siteIds,
      layout: "fitColumns",
      pagination: "local",
      paginationSize: 15,
      columns: [
        {title: "Site ID", field: "siteId", sorter: "string", headerFilter: "input",},
        {title: "Vendor Name", field: "vendorName", sorter: "string", headerFilter: "input"},
        {title: "Vendor ID", field: "vendorId", sorter: "string", headerFilter: "input"},
        {title: "Site Name", field: "siteName", sorter: "string", headerFilter: "input"},
        {title: "Actions", formatter: function(cell, formatterParams, onRendered){
          const siteId = cell.getRow().getData().siteId;
          return `<button class="print-btn" data-site-id="${siteId}">Print QR</button>`;
        }}
      ]
    });
    
    // Add event listener for print buttons
    document.addEventListener('click', function(e) {
      if (e.target && e.target.classList.contains('print-btn')) {
        const siteId = e.target.getAttribute('data-site-id');
        printSingleQR(siteId);
      }
    });
    
    // Function to print a single QR code
    function printSingleQR(siteId) {
      // Find the site data
      const site = siteIds.find(site => site.siteId === siteId);
      if (!site) return;
      
      // Generate URL
      const url = `https://sitefotos.com/s/wells-fargo/janitorial-form/${site.siteId}`;
      
      // Create a unique ID for this print job
      const printId = 'print-frame-' + Date.now();
      
      // Create a new iframe for this print job
      const printFrame = document.createElement('iframe');
      printFrame.id = printId;
      printFrame.name = printId;
      printFrame.style.display = 'none';
      printFramesContainer.appendChild(printFrame);
      
      // Wait for iframe to load
      printFrame.onload = function() {
        // Get the document inside the iframe
        const frameDoc = printFrame.contentDocument || printFrame.contentWindow.document;
        
        // Create the HTML content
        const htmlContent = `
          <!DOCTYPE html>
          <html>
          <head>
            <title>Print QR Code - ${site.siteId}</title>
            <style>
              @page {
                size: 210mm 297mm;
                margin: 0;
              }
              html, body {
                width: 100%;
                height: 100vh;
                overflow: hidden;
                margin: 0;
                padding: 0;
                font-family: Arial, sans-serif;
                display: flex;
                align-items: center;
                justify-content: center;
                page-break-after: avoid;
                break-after: avoid;
              }
              .qr-container {
                text-align: center;
                max-width: 500px;
              }
              h2 {
                font-size: 24pt;
                margin-bottom: 10px;
                margin-top: 0;
              }
              h3 {
                font-size: 18pt;
                margin-bottom: 20px;
                margin-top: 0;
              }
              #qr-code-container {
                display: flex;
                justify-content: center;
              }
            </style>
          </head>
          <body>
            <div class="qr-container">
              <h2>WF - ${site.siteId}</h2>
              <h3>${site.siteName}</h3>
              <div id="qr-code-container"></div>
            </div>
          </body>
          </html>
        `;
        
        // Write the content to the iframe
        frameDoc.open();
        frameDoc.write(htmlContent);
        frameDoc.close();
        
        // Add the QR code script after the document is written
        const script = frameDoc.createElement('script');
        script.src = "https://unpkg.com/qr-code-styling@1.5.0/lib/qr-code-styling.js";
        script.onload = function() {
          // Create and add the QR code after the script is loaded
          const qrScript = frameDoc.createElement('script');
          qrScript.textContent = `
            // Create and render QR code
            const qrCodeInstance = new QRCodeStyling({
              width: 250,
              height: 250,
              data: "${url}",
              type: "svg",
              image: "https://sitefotos.com/images/sitefotos_logo_icon.svg",
              dotsOptions: {
                color: "#000",
                type: "rounded"
              },
              backgroundOptions: {
                color: "#fff",
              },
              imageOptions: {
                crossOrigin: "anonymous",
                margin: 10
              }
            });
            
            // Append QR code to container
            qrCodeInstance.append(document.getElementById("qr-code-container"));
            
            // Print after QR code is rendered
            setTimeout(() => {
              window.print();
              
              // Remove iframe after printing
              window.onafterprint = () => {
                if (window.frameElement) {
                  window.frameElement.remove();
                }
              };
            }, 800);
          `;
          frameDoc.body.appendChild(qrScript);
        };
        frameDoc.head.appendChild(script);
        
        // Focus the iframe for printing
        printFrame.contentWindow.focus();
      };
      
      // Set the iframe source to trigger the load event
      printFrame.src = 'about:blank';
    }
    
   
  </script>
</body>
</html>
