const service = require('../services/wellsfargo.services')
const {awaitSafeQuery, awaitQuery} = require('../utils/db');
const { redisPersistentClient } = require('../utils/redis-persistent');
const {login} = require("../utils/vendor");
const Hashids = require('hashids/cjs');

function deepSearchArrays(obj, targetKey, condition) {
    const results = [];

    function search(currentObj) {
        if (Array.isArray(currentObj)) {
            for (const item of currentObj) {
                search(item);
            }
        } else if (typeof currentObj === 'object' && currentObj !== null) {
            for (const key in currentObj) {
                if (key === targetKey && condition(key, currentObj[key])) {
                    results.push(currentObj);
                }
                search(currentObj[key]);
            }
        }
    }

    search(obj);
    return results;
}
const wellsFargoForm = async (req, res, next) => {
    try {
        
        let internalId = req.params.id;

        //check if id is numeric
        if(!/^\d+$/.test(internalId)) {
            return res.status(400).send({ success: false, message: 'Invalid site id' });
        }

        let janitorialSites = await service.getJanitorialVendorMapping();
        let rollupSites = await awaitQuery(`select sscm_vendor_id, sscm_site_id, scs_client_internal_id, scs_client_site_name from sitefotos_site_client_mapping_extended_ungrouped where scs_client_vendor_id = 15183`);
        let forms = JSON.parse(await redisPersistentClient.get("wellsfargo_forms"));


        internalId = "WF - " + internalId;
        janitorialSites = janitorialSites.filter(x => x.id == internalId)
        if(!janitorialSites.length) {
            return res.status(404).send({ success: false, message: 'Site not found' });
        }
        let vendorId = janitorialSites[0].vendor_id;
        const [vendor] = await awaitQuery(`SELECT * FROM maptile_vendors WHERE vendor_id = ?`, [vendorId]);
        if (!vendor) {
            return res.status(404).send({ success: false, message: 'Vendor not found' });
        }
        let email = "wellsfargo@" + vendor.vendor_email.split('@')[1];
        let formId = forms.find(x => x.vendor_id == vendorId && x.trade_id == 2)?.id;
        let [form] = await awaitQuery(`SELECT * FROM sitefotos_forms WHERE sf_id = ? AND sf_vendor_id = ?`, [formId, vendorId]);
        if (!form) {
            return res.status(404).send({ success: false, message: 'Form not found' });
        }
        let siteId = rollupSites.find(x => x.scs_client_internal_id == internalId && x.sscm_vendor_id == vendorId)?.sscm_site_id;
        let siteName = rollupSites.find(x => x.scs_client_internal_id == internalId && x.sscm_vendor_id == vendorId)?.scs_client_site_name;
        
        
        if(vendorId != form.sf_vendor_id) {
            return res.status(404).send({ success: false, message: 'Form not found' });
        }
        
        const [site] = await awaitQuery(`SELECT * FROM maptile_building WHERE mb_id = ?`, [siteId]);
        const foundServices = [];
        const data2 = JSON.parse(form.sf_form_data_app);
        const found = deepSearchArrays(data2, 'type', (_key, value) => value === 'service');
        const serviceIDs = found.map((item) => item.service ?? null).filter((id) => id !== null);
        foundServices.push(...serviceIDs);
        let services = [];
        if(serviceIDs.length > 0)
        services = await awaitQuery(`select vs_service_id, vs_service_name, vs_service_category, vs_service_options from vendor_services where vs_service_id in (${serviceIDs.join(',')})`);
       
        let data = {
            formId: formId,
            formName: form.sf_form_name,
            siteId: siteId,
            siteName: siteName,
            vendorId: vendorId,
            services: services,
            siteLat: site?.mb_lat,
            siteLon: site?.mb_long,
            formData: JSON.parse(form.sf_form_data_app),
            accessCode: vendor.vendor_access_code,
            email: email
        }
        res.render('forms/wells-fargo-form.ejs', { data });
    } catch (error) {
        res.status(500).send({ success: false, message: error.message });
        next(error);
    }
}

const generateWellsFargoQR = async (req, res) => {
  try {
    
    let janitorialSites = await service.getJanitorialVendorMapping();
    console.log("janitorialSites:", janitorialSites);
   

    
    let rollupSites = await awaitQuery(`select sscm_vendor_id, sscm_site_id, scs_client_internal_id, scs_client_site_name,vendor_company_name from sitefotos_site_client_mapping_extended_ungrouped left join maptile_vendors on vendor_id=sscm_vendor_id where scs_client_vendor_id = 15183`);
    let siteIds = [];
    for (const janitorialSite of janitorialSites) { 
        let site = rollupSites.find(x => x.scs_client_internal_id == janitorialSite.id && x.sscm_vendor_id == janitorialSite.vendor_id);
        if (site) {
            let id = janitorialSite.id.match(/\d+$/)[0];
            siteIds.push({
                siteId: id,
                siteName: site.scs_client_site_name,
                vendorName: site.vendor_company_name,
                vendorId: site.sscm_vendor_id
            });
        }
    }
    

    let data = { siteIds }
    res.render('misc/qr-code.ejs', { data });
  } catch (err) {
    console.error(err);
    res.status(500).send("Error generating QR codes");
  }
}
  
module.exports = {
    wellsFargoForm,
    generateWellsFargoQR
}

